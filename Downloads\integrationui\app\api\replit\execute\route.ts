import { NextResponse } from "next/server"

// Execute commands in a Repl
export async function POST(request: Request) {
  const { replitId, command } = await request.json()

  try {
    const response = await fetch("https://replit.com/graphql", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.REPLIT_API_TOKEN}`,
        "X-Requested-With": "XMLHttpRequest",
      },
      body: JSON.stringify({
        query: `
          mutation ExecuteCommand($replId: String!, $command: String!) {
            executeCommand(replId: $replId, command: $command) {
              ... on ExecuteCommandResult {
                output
                exitCode
              }
            }
          }
        `,
        variables: {
          replId: replitId,
          command,
        },
      }),
    })

    if (!response.ok) {
      throw new Error(`Failed to execute command: ${response.statusText}`)
    }

    const data = await response.json()
    return NextResponse.json(data.data.executeCommand)
  } catch (error) {
    console.error("Error executing command:", error)
    return NextResponse.json({ error: "Failed to execute command" }, { status: 500 })
  }
}
