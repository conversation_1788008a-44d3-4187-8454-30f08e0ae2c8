"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Input } from "@/components/ui/input"

type TerminalEntry = {
  id: string
  command: string
  output: string
  error?: string
  timestamp: string
  isExecuting?: boolean
}

type VirtualMachineProps = {
  logs: string[]
  sandboxId?: string | null
}

export default function VirtualMachine({ logs, sandboxId }: VirtualMachineProps) {
  const [terminalHistory, setTerminalHistory] = useState<TerminalEntry[]>([])
  const [currentCommand, setCurrentCommand] = useState("")
  const [isExecuting, setIsExecuting] = useState(false)
  const [commandHistory, setCommandHistory] = useState<string[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const terminalRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Auto-scroll to bottom when new entries are added
  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight
    }
  }, [terminalHistory])

  // Focus input when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  const executeCommand = async (command: string) => {
    if (!command.trim() || !sandboxId) return

    const entryId = `cmd-${Date.now()}`
    const newEntry: TerminalEntry = {
      id: entryId,
      command: command.trim(),
      output: "",
      timestamp: new Date().toISOString(),
      isExecuting: true,
    }

    setTerminalHistory((prev) => [...prev, newEntry])
    setCurrentCommand("")
    setIsExecuting(true)

    // Add to command history
    setCommandHistory((prev) => [...prev, command.trim()])
    setHistoryIndex(-1)

    try {
      const response = await fetch("/api/e2b/terminal", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          sandboxId,
          command: command.trim(),
        }),
      })

      const result = await response.json()

      setTerminalHistory((prev) =>
        prev.map((entry) =>
          entry.id === entryId
            ? {
                ...entry,
                output: result.stdout || "",
                error: result.stderr || (result.success ? undefined : result.error),
                isExecuting: false,
              }
            : entry,
        ),
      )
    } catch (error) {
      setTerminalHistory((prev) =>
        prev.map((entry) =>
          entry.id === entryId
            ? {
                ...entry,
                output: "",
                error: "Failed to execute command",
                isExecuting: false,
              }
            : entry,
        ),
      )
    } finally {
      setIsExecuting(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      executeCommand(currentCommand)
    } else if (e.key === "ArrowUp") {
      e.preventDefault()
      if (commandHistory.length > 0) {
        const newIndex = historyIndex === -1 ? commandHistory.length - 1 : Math.max(0, historyIndex - 1)
        setHistoryIndex(newIndex)
        setCurrentCommand(commandHistory[newIndex])
      }
    } else if (e.key === "ArrowDown") {
      e.preventDefault()
      if (historyIndex >= 0) {
        const newIndex = historyIndex + 1
        if (newIndex >= commandHistory.length) {
          setHistoryIndex(-1)
          setCurrentCommand("")
        } else {
          setHistoryIndex(newIndex)
          setCurrentCommand(commandHistory[newIndex])
        }
      }
    }
  }

  return (
    <div className="bg-[#1a1b26] h-full flex flex-col text-[#c0caf5] font-mono text-sm">
      <div className="bg-[#101116] p-2 flex items-center justify-between border-b border-gray-700">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
        </div>
        <div className="text-xs text-gray-400">user@metatron: /app</div>
        <div></div>
      </div>

      <div ref={terminalRef} className="flex-1 p-4 overflow-y-auto">
        {logs.map((log, index) => (
          <p key={`log-${index}`} className="whitespace-pre-wrap mb-1">
            <span className="text-gray-500 mr-2">$</span>
            {log}
          </p>
        ))}

        {terminalHistory.map((entry) => (
          <div key={entry.id} className="mb-2">
            <div className="flex items-center">
              <span className="text-[#bb9af7]">user@metatron</span>
              <span className="text-gray-400">:</span>
              <span className="text-[#7aa2f7]">/app</span>
              <span className="text-gray-400 mr-2">$</span>
              <span className="text-[#c0caf5]">{entry.command}</span>
            </div>

            {entry.isExecuting ? (
              <div className="text-yellow-400 ml-4">Executing...</div>
            ) : (
              <>
                {entry.output && <pre className="whitespace-pre-wrap ml-4 text-[#9ece6a]">{entry.output}</pre>}
                {entry.error && <pre className="whitespace-pre-wrap ml-4 text-red-400">{entry.error}</pre>}
              </>
            )}
          </div>
        ))}

        <div className="flex items-center mt-2">
          <span className="text-[#bb9af7]">user@metatron</span>
          <span className="text-gray-400">:</span>
          <span className="text-[#7aa2f7]">/app</span>
          <span className="text-gray-400 mr-2">$</span>
          <Input
            ref={inputRef}
            value={currentCommand}
            onChange={(e) => setCurrentCommand(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={isExecuting || !sandboxId}
            className="bg-transparent border-none text-[#c0caf5] font-mono text-sm p-0 h-auto focus-visible:ring-0 focus-visible:ring-offset-0"
            placeholder={sandboxId ? "Enter command..." : "No active sandbox"}
          />
          {isExecuting && <span className="bg-yellow-400 w-2 h-4 ml-2 animate-pulse"></span>}
        </div>
      </div>

      <div className="bg-[#101116] p-2 text-xs border-t border-gray-700 text-gray-500">
        STATUS: {sandboxId ? "CONNECTED" : "DISCONNECTED"} | SESSION: {terminalHistory.length} |
        {sandboxId ? " INTERACTIVE MODE" : " READ-ONLY MODE"}
      </div>
    </div>
  )
}
