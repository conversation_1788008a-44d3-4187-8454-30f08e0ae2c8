"use client"

type ReplitTerminalProps = {
  logs: string[]
}

export default function ReplitTerminal({ logs }: ReplitTerminalProps) {
  return (
    <div className="bg-[#1a1b26] h-full flex flex-col text-[#c0caf5] font-mono text-sm">
      <div className="bg-[#101116] p-2 flex items-center justify-between border-b border-gray-700">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
        </div>
        <div className="text-xs text-gray-400">replit@workspace: ~/</div>
        <div></div>
      </div>
      <div className="flex-1 p-4 overflow-y-auto">
        {logs.map((log, index) => (
          <p key={index} className="whitespace-pre-wrap">
            <span className="text-gray-500 mr-2">{`$`}</span>
            {log}
          </p>
        ))}
        <div className="flex items-center mt-2">
          <span className="text-[#bb9af7]">replit@workspace</span>
          <span className="text-gray-400">:</span>
          <span className="text-[#7aa2f7]">~/</span>
          <span className="text-gray-400">$</span>
          <span className="bg-gray-400 w-2 h-4 ml-2 animate-pulse"></span>
        </div>
      </div>
      <div className="bg-[#101116] p-2 text-xs border-t border-gray-700 text-gray-500">
        STATUS: RUNNING | REPLIT | CPU: 12% | MEM: 18%
      </div>
    </div>
  )
}
