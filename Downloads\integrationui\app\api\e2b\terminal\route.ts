import { NextResponse } from "next/server"
import { Sandbox } from "e2b"

export async function POST(request: Request) {
  const { sandboxId, command } = await request.json()

  if (!sandboxId || !command) {
    return NextResponse.json({ error: "Sandbox ID and command are required" }, { status: 400 })
  }

  try {
    const sandbox = await Sandbox.connect(sandboxId, {
      apiKey: process.env.E2B_API_KEY,
    })

    // Execute the command in the sandbox
    const result = await sandbox.commands.run(command, {
      onStdout: (data) => {
        console.log("[v0] Terminal stdout:", data)
      },
      onStderr: (data) => {
        console.log("[v0] Terminal stderr:", data)
      },
    })

    return NextResponse.json({
      success: true,
      stdout: result.stdout,
      stderr: result.stderr,
      exitCode: result.exitCode,
      command,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Terminal command failed:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Command execution failed",
        command,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    )
  }
}
