import { Textarea } from "@/components/ui/textarea"
import type { ReplitFile } from "@/lib/types"

type ReplitCodeViewerProps = {
  file: ReplitFile | null
}

export default function ReplitCodeViewer({ file }: ReplitCodeViewerProps) {
  if (!file) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        <p>Select a file to view its content</p>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-background">
      <div className="p-2 border-b text-sm text-muted-foreground flex items-center justify-between">
        <span>{file.name}</span>
        {file.language && <span className="text-xs bg-secondary px-2 py-1 rounded">{file.language}</span>}
      </div>
      <Textarea
        value={file.content || "/* This file is empty. */"}
        readOnly
        className="flex-1 bg-secondary border-none font-mono text-sm resize-none rounded-none focus-visible:ring-0"
      />
    </div>
  )
}
