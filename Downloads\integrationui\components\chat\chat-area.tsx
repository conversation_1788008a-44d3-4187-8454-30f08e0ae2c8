"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import {
  Star,
  Paperclip,
  Settings,
  Mic,
  Camera,
  Send,
  PanelRightOpen,
  PanelLeftOpen,
  PanelRightClose,
  LoaderCircle,
  ScreenShare,
  X,
  Layers,
  Search,
  Database,
  FileText,
  Brain,
} from "lucide-react"
import ChatMessage from "./chat-message"
import type { ChatMessage as ChatMessageType } from "@/lib/types"
import { ThemeToggle } from "../theme-toggle"
import ThemeAwareLogo from "../theme-aware-logo"
import { cn } from "@/lib/utils"

type ChatAreaProps = {
  isLeftPanelOpen: boolean
  toggleLeftPanel: () => void
  isRightPanelOpen: boolean
  toggleRightPanel: () => void
  isSandboxPanelOpen: boolean
  toggleSandboxPanel: () => void
  isSecondaryPanelOpen: boolean
  toggleSecondaryPanel: () => void
  onNewSandbox: (sandboxId: string) => void
  onNewReplit: (replitId: string) => void
}

export default function ChatArea({
  isLeftPanelOpen,
  toggleLeftPanel,
  isRightPanelOpen,
  toggleRightPanel,
  isSandboxPanelOpen,
  toggleSandboxPanel,
  isSecondaryPanelOpen,
  toggleSecondaryPanel,
  onNewSandbox,
  onNewReplit,
}: ChatAreaProps) {
  const [prompt, setPrompt] = useState("")
  const [messages, setMessages] = useState<ChatMessageType[]>([
    {
      id: "initial-message",
      sender: "ai",
      text: "Hello! I am Metatron, your E2B Code Execution Agent. I can help you with:\n\n• Running Python code and scripts\n• File operations (read, write, upload, download)\n• Command execution in secure sandbox\n• Starting web servers and getting public URLs\n• Data analysis and visualization\n• Managing sandbox environments\n\nTry asking me to 'run python code to generate fibonacci numbers' or 'create a simple web server'!",
      timestamp: Date.now(),
    },
  ])
  const [currentSandboxId, setCurrentSandboxId] = useState<string | null>(null)
  const [isTyping, setIsTyping] = useState(false)
  const chatContainerRef = useRef<HTMLDivElement>(null)

  const [cameraStream, setCameraStream] = useState<MediaStream | null>(null)
  const [screenStream, setScreenStream] = useState<MediaStream | null>(null)
  const cameraPreviewRef = useRef<HTMLVideoElement>(null)
  const screenPreviewRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
    }
  }, [messages])

  const stopStream = (stream: MediaStream | null, setter: (stream: MediaStream | null) => void) => {
    if (stream) {
      stream.getTracks().forEach((track) => track.stop())
      setter(null)
    }
  }

  const handleCameraToggle = async () => {
    if (cameraStream) {
      stopStream(cameraStream, setCameraStream)
    } else {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true })
        setCameraStream(stream)
      } catch (err) {
        console.error("Error accessing camera:", err)
      }
    }
  }

  const handleScreenShareToggle = async () => {
    if (screenStream) {
      stopStream(screenStream, setScreenStream)
    } else {
      try {
        const stream = await navigator.mediaDevices.getDisplayMedia({ video: { cursor: "always" }, audio: false })
        stream.getVideoTracks()[0].addEventListener("ended", () => {
          setScreenStream(null)
        })
        setScreenStream(stream)
      } catch (err) {
        console.error("Error accessing screen share:", err)
      }
    }
  }

  useEffect(() => {
    if (cameraPreviewRef.current && cameraStream) {
      cameraPreviewRef.current.srcObject = cameraStream
    }
  }, [cameraStream])

  useEffect(() => {
    if (screenPreviewRef.current && screenStream) {
      screenPreviewRef.current.srcObject = screenStream
    }
  }, [screenStream])

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!prompt.trim() || isTyping) return

    const userMessage: ChatMessageType = {
      id: crypto.randomUUID(),
      sender: "user",
      text: prompt,
      timestamp: Date.now(),
    }
    setMessages((prev) => [...prev, userMessage])
    const currentPrompt = prompt
    setPrompt("")
    setIsTyping(true)

    try {
      // Use the E2B Agent for all requests
      const response = await fetch("/api/e2b/agent", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt: currentPrompt,
          sandboxId: currentSandboxId
        }),
      })

      if (!response.ok) throw new Error("Failed to process request with E2B Agent")

      const { sandboxId, response: agentResponse } = await response.json()

      // Update current sandbox ID if we got a new one
      if (sandboxId && sandboxId !== currentSandboxId) {
        setCurrentSandboxId(sandboxId)
        onNewSandbox(sandboxId)
      }

      const aiMessage: ChatMessageType = {
        id: crypto.randomUUID(),
        sender: "ai",
        text: agentResponse,
        timestamp: Date.now(),
      }
      setMessages((prev) => [...prev, aiMessage])

      // Handle special cases for legacy compatibility
      if (currentPrompt.toLowerCase().includes("dashboard") ||
          currentPrompt.toLowerCase().includes("presentation") ||
          currentPrompt.toLowerCase().includes("data analysis") ||
          currentPrompt.toLowerCase().includes("chart")) {
        // Also trigger Replit for data visualization requests
        try {
          const replitResponse = await fetch("/api/replit/run", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ prompt: currentPrompt }),
          })
          if (replitResponse.ok) {
            const { replitId } = await replitResponse.json()
            onNewReplit(replitId)

            const replitMessage: ChatMessageType = {
              id: crypto.randomUUID(),
              sender: "ai",
              text: `I've also created a data dashboard in Replit (ID: ${replitId}) for enhanced visualization.`,
              timestamp: Date.now(),
            }
            setMessages((prev) => [...prev, replitMessage])
          }
        } catch (replitError) {
          console.error("Replit integration failed:", replitError)
        }
      }
    } catch (error) {
      console.error("E2B Agent Error:", error)
      const aiMessage: ChatMessageType = {
        id: crypto.randomUUID(),
        sender: "ai",
        text: "Sorry, I encountered an error processing your request with the E2B Agent. Please try again or check if the sandbox is still running.",
        timestamp: Date.now(),
      }
      setMessages((prev) => [...prev, aiMessage])
    } finally {
      setIsTyping(false)
    }
  }

  return (
    <div className="flex-1 flex flex-col bg-background">
      <header className="h-14 border-b flex items-center justify-between px-4">
        <div className="w-48 flex justify-start">
          {!isLeftPanelOpen && (
            <Button variant="ghost" size="icon" onClick={toggleLeftPanel} className="text-muted-foreground">
              <PanelLeftOpen className="w-5 h-5" />
            </Button>
          )}
        </div>
        <div className="flex-1 flex justify-center items-center space-x-4">
          <ThemeAwareLogo />
          {currentSandboxId && (
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>E2B Sandbox: {currentSandboxId.slice(0, 8)}...</span>
            </div>
          )}
        </div>
        <div className="w-48 flex justify-end">
          <ThemeToggle />
          {!isRightPanelOpen && (
            <Button variant="ghost" size="icon" onClick={toggleRightPanel} className="text-muted-foreground ml-2">
              <PanelRightOpen className="w-5 h-5" />
            </Button>
          )}
        </div>
      </header>
      <div ref={chatContainerRef} className="flex-1 overflow-y-auto p-6 space-y-6">
        {messages.map((msg) => (
          <ChatMessage key={msg.id} sender={msg.sender} text={msg.text} />
        ))}
        {isTyping && (
          <div className="flex justify-start">
            <div className="max-w-xl px-4 py-2.5 rounded-2xl bg-secondary text-muted-foreground rounded-bl-none flex items-center">
              <LoaderCircle className="w-5 h-5 animate-spin" />
            </div>
          </div>
        )}
      </div>
      <div className="p-4 border-t bg-card">
        <div className="flex gap-2 pb-2">
          {cameraStream && (
            <div className="relative w-40 h-28 bg-black rounded-lg">
              <video ref={cameraPreviewRef} autoPlay muted className="w-full h-full object-cover rounded-md" />
              <Button
                variant="destructive"
                size="icon"
                className="absolute top-1 right-1 h-6 w-6"
                onClick={handleCameraToggle}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          )}
          {screenStream && (
            <div className="relative w-40 h-28 bg-black rounded-lg">
              <video ref={screenPreviewRef} autoPlay muted className="w-full h-full object-contain rounded-md" />
              <Button
                variant="destructive"
                size="icon"
                className="absolute top-1 right-1 h-6 w-6"
                onClick={handleScreenShareToggle}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>

        {/* E2B Quick Actions */}
        <div className="flex flex-wrap gap-2 mb-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPrompt("Write Python code to generate the first 10 Fibonacci numbers and calculate their sum and average")}
            className="text-xs"
          >
            <Brain className="w-3 h-3 mr-1" />
            Fibonacci
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPrompt("Create a simple Python web server on port 8000")}
            className="text-xs"
          >
            <Settings className="w-3 h-3 mr-1" />
            Web Server
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPrompt("List all files in the current directory")}
            className="text-xs"
          >
            <FileText className="w-3 h-3 mr-1" />
            List Files
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPrompt("Run command: python3 --version && pip list")}
            className="text-xs"
          >
            <Search className="w-3 h-3 mr-1" />
            System Info
          </Button>
        </div>

        <form onSubmit={handleSendMessage} className="bg-secondary p-3 rounded-lg">
          <div className="relative">
            <Input
              placeholder="Enter your prompt"
              className="bg-transparent border-none focus-visible:ring-0 focus-visible:ring-offset-0 text-foreground placeholder:text-muted-foreground w-full pr-32"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              disabled={isTyping}
            />
            <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center space-x-2">
              <Button
                type="submit"
                variant="ghost"
                size="icon"
                className="text-muted-foreground hover:text-foreground"
                disabled={isTyping || !prompt.trim()}
              >
                {isTyping ? <LoaderCircle className="w-5 h-5 animate-spin" /> : <Send className="w-5 h-5" />}
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className={cn(
                  "text-muted-foreground hover:text-foreground",
                  isSandboxPanelOpen && "bg-accent-primary text-primary-foreground hover:bg-accent-secondary",
                )}
                onClick={toggleSandboxPanel}
              >
                {isSandboxPanelOpen ? <PanelRightClose className="w-5 h-5" /> : <PanelRightOpen className="w-5 h-5" />}
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className={cn(
                  "text-muted-foreground hover:text-foreground",
                  isSecondaryPanelOpen && "bg-accent-primary text-primary-foreground hover:bg-accent-secondary",
                )}
                onClick={toggleSecondaryPanel}
              >
                <Layers className="w-5 h-5" />
              </Button>
            </div>
          </div>
          <div className="flex items-center justify-between mt-3 pt-3 border-t">
            <div className="flex items-center space-x-2">
              <Button type="button" variant="ghost" size="icon" className="text-muted-foreground hover:text-foreground">
                <Star className="w-5 h-5" />
              </Button>
              <Button type="button" variant="ghost" size="icon" className="text-muted-foreground hover:text-foreground">
                <Paperclip className="w-5 h-5" />
              </Button>
              <Button type="button" variant="ghost" size="icon" className="text-muted-foreground hover:text-foreground">
                <Settings className="w-5 h-5" />
              </Button>
              <Button type="button" variant="ghost" size="icon" className="text-muted-foreground hover:text-foreground">
                <Mic className="w-5 h-5" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={handleCameraToggle}
                className={cn(
                  "text-muted-foreground hover:text-foreground",
                  cameraStream && "text-accent-primary hover:text-accent-primary/80",
                )}
              >
                <Camera className="w-5 h-5" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={handleScreenShareToggle}
                className={cn(
                  "text-muted-foreground hover:text-foreground",
                  screenStream && "text-accent-primary hover:text-accent-primary/80",
                )}
              >
                <ScreenShare className="w-5 h-5" />
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 hover:text-blue-300 px-3 py-1 h-8"
              >
                <Search className="w-4 h-4 mr-1" />
                Search
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="bg-green-500/20 text-green-400 hover:bg-green-500/30 hover:text-green-300 px-3 py-1 h-8"
              >
                <Database className="w-4 h-4 mr-1" />
                Data
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="bg-orange-500/20 text-orange-400 hover:bg-orange-500/30 hover:text-orange-300 px-3 py-1 h-8"
              >
                <FileText className="w-4 h-4 mr-1" />
                Docs
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="bg-purple-500/20 text-purple-400 hover:bg-purple-500/30 hover:text-purple-300 px-3 py-1 h-8"
              >
                <Brain className="w-4 h-4 mr-1" />
                AGI
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
