type ChatMessageProps = {
  sender: "user" | "ai"
  text: string
}

export default function ChatMessage({ sender, text }: ChatMessageProps) {
  const isUser = sender === "user"

  return (
    <div className={`flex ${isUser ? "justify-end" : "justify-start"}`}>
      <div
        className={`max-w-xl px-4 py-2.5 rounded-2xl ${
          isUser
            ? "bg-accent-primary text-primary-foreground rounded-br-none"
            : "bg-secondary text-secondary-foreground rounded-bl-none"
        }`}
      >
        <p className="text-sm leading-relaxed whitespace-pre-wrap">{text}</p>
      </div>
    </div>
  )
}
