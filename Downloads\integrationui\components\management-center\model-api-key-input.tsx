"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON>, <PERSON>Off, Co<PERSON>, Save } from "lucide-react"

type ModelApiKeyInputProps = {
  id: string
  label: string
  models: { value: string; label: string }[]
}

export default function ModelApiKeyInput({ id, label, models }: ModelApiKeyInputProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [value, setValue] = useState("********************************")
  const [isEditing, setIsEditing] = useState(false)

  const handleCopy = () => {
    navigator.clipboard.writeText(value)
  }

  const handleSaveClick = () => {
    console.log(`Saving ${label}:`, value)
    setIsEditing(false)
  }

  return (
    <div className="space-y-2">
      <Label className="text-sm font-medium text-foreground">{label}</Label>
      <div className="p-4 bg-secondary rounded-lg border space-y-3">
        <div>
          <Label htmlFor={`${id}-model`} className="text-xs text-muted-foreground">
            Model
          </Label>
          <Select defaultValue={models[0]?.value}>
            <SelectTrigger id={`${id}-model`} className="w-full bg-background">
              <SelectValue placeholder="Select a model" />
            </SelectTrigger>
            <SelectContent>
              {models.map((model) => (
                <SelectItem key={model.value} value={model.value}>
                  {model.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor={id} className="text-xs text-muted-foreground">
            API Key
          </Label>
          <div className="flex items-center gap-2">
            <div className="relative flex-grow">
              <Input
                id={id}
                type={isVisible ? "text" : "password"}
                value={value}
                onChange={(e) => {
                  setValue(e.target.value)
                  if (!isEditing) setIsEditing(true)
                }}
                className="bg-background border font-mono"
              />
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-2 top-1/2 -translate-y-1/2 h-7 w-7 text-muted-foreground hover:bg-accent"
                onClick={() => setIsVisible(!isVisible)}
              >
                {isVisible ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </Button>
            </div>
            <Button variant="outline" size="icon" className="h-9 w-9 bg-transparent" onClick={handleCopy}>
              <Copy className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              className="bg-accent-primary text-primary-foreground hover:bg-accent-secondary"
              onClick={handleSaveClick}
              disabled={!isEditing}
            >
              <Save className="w-4 h-4 mr-2" />
              Save
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
