"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Save, Wrench } from "lucide-react"
import ApiKeyInput from "@/components/management-center/api-key-input"
import type { AgentConfig } from "@/lib/agent-types"

type AgentCardProps = {
  agent: AgentConfig
  onSave: (agent: AgentConfig) => void
}

const agentRoles = ["Coder", "Designer", "Researcher", "Analyst", "Writer", "Project Manager", "Generalist"]

export default function AgentCard({ agent, onSave }: AgentCardProps) {
  const [config, setConfig] = useState<AgentConfig>(agent)

  useEffect(() => {
    setConfig(agent)
  }, [agent])

  const handleSave = () => {
    onSave(config)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <Input
          value={config.name}
          onChange={(e) => setConfig({ ...config, name: e.target.value })}
          className="text-lg font-bold border-none focus-visible:ring-1 focus-visible:ring-ring p-1 -ml-1 w-2/3"
        />
        <Button
          size="sm"
          onClick={handleSave}
          className="bg-accent-primary text-primary-foreground hover:bg-accent-secondary"
        >
          <Save className="w-4 h-4 mr-2" />
          Save
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label className="text-sm font-medium text-muted-foreground">API Key</Label>
          <ApiKeyInput
            id={`api-key-${config.id}`}
            label=""
            initialValue={config.apiKey}
            onSave={(value) => setConfig({ ...config, apiKey: value })}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor={`model-${config.id}`} className="text-sm font-medium text-muted-foreground">
            Model
          </Label>
          <Select value={config.model} onValueChange={(value) => setConfig({ ...config, model: value })}>
            <SelectTrigger id={`model-${config.id}`} className="w-full">
              <SelectValue placeholder="Select a model" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="gemini-pro">Google Gemini Pro</SelectItem>
              <SelectItem value="gpt-4">OpenAI GPT-4</SelectItem>
              <SelectItem value="claude-3">Anthropic Claude 3</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor={`role-${config.id}`} className="text-sm font-medium text-muted-foreground">
            Agent Role
          </Label>
          <Select value={config.role} onValueChange={(value) => setConfig({ ...config, role: value })}>
            <SelectTrigger id={`role-${config.id}`} className="w-full">
              <SelectValue placeholder="Select a role" />
            </SelectTrigger>
            <SelectContent>
              {agentRoles.map((role) => (
                <SelectItem key={role} value={role}>
                  {role}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor={`prompt-${config.id}`} className="text-sm font-medium text-muted-foreground">
            System Prompt
          </Label>
          <Textarea
            id={`prompt-${config.id}`}
            placeholder="Enter the agent's base instructions..."
            value={config.prompt}
            onChange={(e) => setConfig({ ...config, prompt: e.target.value })}
            className="min-h-[100px]"
          />
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium text-muted-foreground">Tools</Label>
          <Button variant="outline" className="w-full justify-start bg-transparent">
            <Wrench className="w-4 h-4 mr-2" />
            Manage Tools ({config.tools.length} connected)
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
