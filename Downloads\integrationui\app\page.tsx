"use client"

import { useState } from "react"
import LeftPanel from "@/components/chat/left-panel"
import ChatArea from "@/components/chat/chat-area"
import RightPanel from "@/components/chat/right-panel"
import SandboxPanel from "@/components/sandbox/sandbox-panel"
import SecondaryPanel from "@/components/secondary/secondary-panel"

export default function Home() {
  const [isLeftPanelOpen, setIsLeftPanelOpen] = useState(true)
  const [isRightPanelOpen, setIsRightPanelOpen] = useState(true)
  const [isSandboxPanelOpen, setIsSandboxPanelOpen] = useState(false)
  const [isSecondaryPanelOpen, setIsSecondaryPanelOpen] = useState(false)
  const [currentSandboxId, setCurrentSandboxId] = useState<string | null>(null)
  const [currentReplitId, setCurrentReplitId] = useState<string | null>(null)

  const toggleLeftPanel = () => setIsLeftPanelOpen(!isLeftPanelOpen)
  const toggleRightPanel = () => setIsRightPanelOpen(!isRightPanelOpen)

  const toggleSandboxPanel = () => {
    setIsSandboxPanelOpen(!isSandboxPanelOpen)
    if (!isSandboxPanelOpen) {
      setIsRightPanelOpen(true)
      setIsSecondaryPanelOpen(false) // Close secondary panel when opening sandbox
    }
  }

  const toggleSecondaryPanel = () => {
    setIsSecondaryPanelOpen(!isSecondaryPanelOpen)
    if (!isSecondaryPanelOpen) {
      setIsRightPanelOpen(true)
      setIsSandboxPanelOpen(false) // Close sandbox panel when opening secondary
    }
  }

  const handleNewSandbox = (sandboxId: string) => {
    setCurrentSandboxId(sandboxId)
    if (!isSandboxPanelOpen) {
      setIsSandboxPanelOpen(true)
      setIsSecondaryPanelOpen(false)
    }
    if (!isRightPanelOpen) {
      setIsRightPanelOpen(true)
    }
  }

  const handleNewReplit = (replitId: string) => {
    setCurrentReplitId(replitId)
    if (!isSecondaryPanelOpen) {
      setIsSecondaryPanelOpen(true)
      setIsSandboxPanelOpen(false)
    }
    if (!isRightPanelOpen) {
      setIsRightPanelOpen(true)
    }
  }

  const renderRightContent = () => {
    if (isSandboxPanelOpen) {
      return <SandboxPanel togglePanel={toggleRightPanel} sandboxId={currentSandboxId} />
    }
    if (isSecondaryPanelOpen) {
      return <SecondaryPanel togglePanel={toggleRightPanel} replitId={currentReplitId} />
    }
    return <RightPanel togglePanel={toggleRightPanel} />
  }

  return (
    <main className="flex h-screen bg-background text-foreground">
      <LeftPanel togglePanel={toggleLeftPanel} />
      <ChatArea
        isLeftPanelOpen={isLeftPanelOpen}
        toggleLeftPanel={toggleLeftPanel}
        isRightPanelOpen={isRightPanelOpen}
        toggleRightPanel={toggleRightPanel}
        isSandboxPanelOpen={isSandboxPanelOpen}
        toggleSandboxPanel={toggleSandboxPanel}
        isSecondaryPanelOpen={isSecondaryPanelOpen}
        toggleSecondaryPanel={toggleSecondaryPanel}
        onNewSandbox={handleNewSandbox}
        onNewReplit={handleNewReplit}
      />
      {isRightPanelOpen && renderRightContent()}
    </main>
  )
}
