"use client"
import Link from "next/link"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  Palette,
  Share2,
  AppWindow,
  BrainCircuit,
  Video,
  Settings2,
  PlusCircle,
  Wind,
  Waypoints,
  Contact,
  PanelRightClose,
  Wrench,
} from "lucide-react"
import FlowTab from "./flow-tab"

const features = [
  { name: "Agent/Flow Builder", icon: <Bot className="w-5 h-5" />, href: "/agent-flow-builder" },
  { name: "Creative Studio", icon: <Palette className="w-5 h-5" />, href: "/creative-studio" },
  { name: "Social Station", icon: <Share2 className="w-5 h-5" />, href: "/social-station" },
  { name: "CRM", icon: <Contact className="w-5 h-5" />, href: "/crm" },
  { name: "Free Flow", icon: <Wind className="w-5 h-5" />, href: "/free-flow" },
  { name: "App/Web Builder", icon: <AppWindow className="w-5 h-5" />, href: "#" },
  { name: "Model Training", icon: <BrainCircuit className="w-5 h-5" />, href: "/model-training" },
  { name: "Video Meeting", icon: <Video className="w-5 h-5" />, href: "#" },
  { name: "Management Center", icon: <Settings2 className="w-5 h-5" />, href: "/management-center" },
]

const addFeatures = ["Add Feature", "Add Feature"]

export default function RightPanel({ togglePanel }: { togglePanel: () => void }) {
  return (
    <aside className="w-72 flex flex-col bg-card border-l relative">
      <Button
        variant="ghost"
        size="icon"
        onClick={togglePanel}
        className="absolute top-2 left-2 z-10 text-muted-foreground hover:bg-secondary hover:text-foreground h-8 w-8"
        title="Collapse panel"
      >
        <PanelRightClose className="w-5 h-5" />
      </Button>
      <Tabs defaultValue="features" className="flex flex-col h-full">
        <TabsList className="grid w-full grid-cols-3 bg-card border-b rounded-none h-14">
          <TabsTrigger
            value="features"
            className="data-[state=active]:bg-accent-primary data-[state=active]:text-primary-foreground text-muted-foreground rounded-none"
          >
            Features
          </TabsTrigger>
          <TabsTrigger
            value="tools"
            className="data-[state=active]:bg-accent-primary data-[state=active]:text-primary-foreground text-muted-foreground rounded-none"
          >
            <Wrench className="w-4 h-4 mr-2" />
            Tools
          </TabsTrigger>
          <TabsTrigger
            value="flow"
            className="data-[state=active]:bg-accent-primary data-[state=active]:text-primary-foreground text-muted-foreground rounded-none"
          >
            <Waypoints className="w-4 h-4 mr-2" />
            Flow
          </TabsTrigger>
        </TabsList>
        <TabsContent value="features" className="flex-1 overflow-y-auto p-3 space-y-2 mt-0">
          {features.map((tool) => (
            <Link
              key={tool.name}
              href={tool.href}
              className="flex items-center p-3 rounded-lg bg-secondary hover:bg-accent cursor-pointer transition-colors"
            >
              <div className="mr-3 text-accent-primary">{tool.icon}</div>
              <p className="font-medium text-foreground text-sm">{tool.name}</p>
            </Link>
          ))}
          {addFeatures.map((feature, index) => (
            <div
              key={index}
              className="flex items-center p-3 rounded-lg bg-secondary hover:bg-accent cursor-pointer transition-colors text-muted-foreground"
            >
              <div className="mr-3">
                <PlusCircle className="w-5 h-5" />
              </div>
              <p className="font-medium text-sm">{feature}</p>
            </div>
          ))}
        </TabsContent>
        <TabsContent value="tools" className="flex-1 overflow-y-auto p-4 mt-0">
          <div className="text-center text-muted-foreground p-8 text-sm">
            <p>Utility tools will be displayed here.</p>
          </div>
        </TabsContent>
        <TabsContent value="flow" className="flex-1 overflow-y-auto p-4 mt-0">
          <FlowTab />
        </TabsContent>
      </Tabs>
    </aside>
  )
}
