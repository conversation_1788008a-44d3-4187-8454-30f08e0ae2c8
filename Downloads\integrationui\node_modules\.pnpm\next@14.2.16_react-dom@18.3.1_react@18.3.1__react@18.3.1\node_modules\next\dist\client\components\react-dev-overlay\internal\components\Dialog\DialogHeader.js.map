{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Dialog/DialogHeader.tsx"], "names": ["DialogHeader", "children", "className", "div", "data-nextjs-dialog-header"], "mappings": ";;;;+BAkBSA;;;eAAAA;;;;;iEAlBc;AAOvB,MAAMA,eAA4C,SAASA,aAAa,KAGvE;IAHuE,IAAA,EACtEC,QAAQ,EACRC,SAAS,EACV,GAHuE;IAItE,qBACE,qBAACC;QAAIC,2BAAyB;QAACF,WAAWA;kBACvCD;;AAGP"}