import { NextResponse } from "next/server"

// Real Replit API integration
export async function POST(request: Request) {
  const { prompt } = await request.json()

  try {
    // Create a new Repl using the Replit API
    const createResponse = await fetch("https://replit.com/graphql", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.REPLIT_API_TOKEN}`,
        "X-Requested-With": "XMLHttpRequest",
      },
      body: JSON.stringify({
        query: `
          mutation CreateRepl($input: CreateReplInput!) {
            createRepl(input: $input) {
              ... on Repl {
                id
                slug
                title
                language
                url
              }
            }
          }
        `,
        variables: {
          input: {
            title: `AI Generated - ${prompt.slice(0, 50)}`,
            language: "python3",
            isPrivate: false,
          },
        },
      }),
    })

    if (!createResponse.ok) {
      throw new Error(`Failed to create Repl: ${createResponse.statusText}`)
    }

    const createData = await createResponse.json()
    const repl = createData.data.createRepl

    if (!repl) {
      throw new Error("Failed to create Repl")
    }

    // Generate appropriate code based on the prompt
    let code = ""
    let filename = "main.py"

    if (prompt.toLowerCase().includes("dashboard") || prompt.toLowerCase().includes("data")) {
      code = generateDashboardCode(prompt)
      filename = "dashboard.py"
    } else if (prompt.toLowerCase().includes("presentation")) {
      code = generatePresentationCode(prompt)
      filename = "presentation.py"
    } else if (prompt.toLowerCase().includes("chart") || prompt.toLowerCase().includes("graph")) {
      code = generateChartCode(prompt)
      filename = "chart.py"
    } else {
      code = generateGenericCode(prompt)
    }

    // Write the generated code to the Repl
    await writeFileToRepl(repl.id, filename, code)

    // Also create requirements.txt if it's a Python project
    if (filename.endsWith(".py")) {
      const requirements = generateRequirements(code)
      await writeFileToRepl(repl.id, "requirements.txt", requirements)
    }

    return NextResponse.json({
      replitId: repl.id,
      url: repl.url,
      title: repl.title,
    })
  } catch (error) {
    console.error("Error creating Repl:", error)
    return NextResponse.json({ error: "Failed to create Repl" }, { status: 500 })
  }
}

async function writeFileToRepl(replId: string, filename: string, content: string) {
  const response = await fetch("https://replit.com/graphql", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.REPLIT_API_TOKEN}`,
      "X-Requested-With": "XMLHttpRequest",
    },
    body: JSON.stringify({
      query: `
        mutation WriteFile($replId: String!, $path: String!, $content: String!) {
          writeFile(replId: $replId, path: $path, content: $content) {
            ... on WriteFileResult {
              success
            }
          }
        }
      `,
      variables: {
        replId,
        path: filename,
        content,
      },
    }),
  })

  if (!response.ok) {
    throw new Error(`Failed to write file: ${response.statusText}`)
  }
}

function generateDashboardCode(prompt: string): string {
  return `import streamlit as st
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

st.set_page_config(page_title="AI Generated Dashboard", layout="wide")

st.title("📊 ${prompt}")
st.write("This dashboard was generated by AI based on your request.")

# Generate sample data
np.random.seed(42)
dates = pd.date_range('2023-01-01', periods=100, freq='D')
values = np.cumsum(np.random.randn(100)) + 100

df = pd.DataFrame({
    'Date': dates,
    'Value': values,
    'Category': np.random.choice(['A', 'B', 'C'], 100)
})

# Main metrics
col1, col2, col3, col4 = st.columns(4)
with col1:
    st.metric("Total Records", len(df))
with col2:
    st.metric("Average Value", f"{df['Value'].mean():.2f}")
with col3:
    st.metric("Max Value", f"{df['Value'].max():.2f}")
with col4:
    st.metric("Min Value", f"{df['Value'].min():.2f}")

# Charts
st.subheader("Data Visualization")

col1, col2 = st.columns(2)

with col1:
    st.subheader("Time Series")
    fig, ax = plt.subplots()
    ax.plot(df['Date'], df['Value'])
    ax.set_xlabel('Date')
    ax.set_ylabel('Value')
    st.pyplot(fig)

with col2:
    st.subheader("Category Distribution")
    category_counts = df['Category'].value_counts()
    fig, ax = plt.subplots()
    ax.pie(category_counts.values, labels=category_counts.index, autopct='%1.1f%%')
    st.pyplot(fig)

# Data table
st.subheader("Raw Data")
st.dataframe(df)

# Run with: streamlit run dashboard.py
`
}

function generatePresentationCode(prompt: string): string {
  return `import streamlit as st

st.set_page_config(page_title="AI Presentation", layout="wide")

# Custom CSS for presentation style
st.markdown("""
<style>
.big-font {
    font-size:50px !important;
    text-align: center;
}
.medium-font {
    font-size:30px !important;
    text-align: center;
}
</style>
""", unsafe_allow_html=True)

# Title slide
st.markdown('<p class="big-font">🎯 ${prompt}</p>', unsafe_allow_html=True)
st.markdown('<p class="medium-font">Generated by AI</p>', unsafe_allow_html=True)

st.divider()

# Content slides
slides = [
    {
        "title": "Overview",
        "content": "This presentation was automatically generated based on your request. It demonstrates the power of AI-driven content creation."
    },
    {
        "title": "Key Points",
        "content": """
        • Automated content generation
        • Interactive web-based presentations
        • Real-time data integration
        • Customizable themes and layouts
        """
    },
    {
        "title": "Benefits",
        "content": """
        ✅ Save time on content creation
        ✅ Consistent formatting and style
        ✅ Easy to update and modify
        ✅ Interactive and engaging
        """
    }
]

for i, slide in enumerate(slides):
    st.header(f"{i+1}. {slide['title']}")
    st.write(slide['content'])
    st.divider()

st.success("Presentation complete! This is running live on Replit.")
`
}

function generateChartCode(prompt: string): string {
  return `import matplotlib.pyplot as plt
import numpy as np
import streamlit as st

st.title("📈 ${prompt}")
st.write("Interactive charts generated by AI")

# Generate sample data
np.random.seed(42)
x = np.linspace(0, 10, 100)
y1 = np.sin(x) + np.random.normal(0, 0.1, 100)
y2 = np.cos(x) + np.random.normal(0, 0.1, 100)

# Create multiple chart types
chart_type = st.selectbox("Select Chart Type", ["Line Chart", "Bar Chart", "Scatter Plot", "Histogram"])

fig, ax = plt.subplots(figsize=(10, 6))

if chart_type == "Line Chart":
    ax.plot(x, y1, label='Series 1', linewidth=2)
    ax.plot(x, y2, label='Series 2', linewidth=2)
    ax.legend()
elif chart_type == "Bar Chart":
    categories = ['A', 'B', 'C', 'D', 'E']
    values = np.random.randint(10, 100, 5)
    ax.bar(categories, values, color='skyblue')
elif chart_type == "Scatter Plot":
    ax.scatter(y1, y2, alpha=0.6, c=x, cmap='viridis')
    ax.set_xlabel('Series 1')
    ax.set_ylabel('Series 2')
elif chart_type == "Histogram":
    ax.hist(y1, bins=20, alpha=0.7, color='lightcoral')
    ax.set_xlabel('Value')
    ax.set_ylabel('Frequency')

ax.set_title(f'{chart_type} - {prompt}')
ax.grid(True, alpha=0.3)

st.pyplot(fig)

# Additional metrics
st.subheader("Data Statistics")
col1, col2 = st.columns(2)
with col1:
    st.write("Series 1 Stats:")
    st.write(f"Mean: {np.mean(y1):.3f}")
    st.write(f"Std: {np.std(y1):.3f}")
with col2:
    st.write("Series 2 Stats:")
    st.write(f"Mean: {np.mean(y2):.3f}")
    st.write(f"Std: {np.std(y2):.3f}")
`
}

function generateGenericCode(prompt: string): string {
  return `import streamlit as st
import datetime

st.title("🤖 AI Generated Application")
st.write(f"Request: {prompt}")

st.info("This application was automatically generated based on your prompt!")

# Basic interactive elements
name = st.text_input("Enter your name:")
if name:
    st.write(f"Hello, {name}!")

# Current time
st.write(f"Generated at: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Simple functionality based on prompt
st.subheader("Generated Content")
st.write("""
This is a basic Streamlit application that was created automatically.
You can extend this code to add more specific functionality based on your needs.
""")

if st.button("Click me!"):
    st.balloons()
    st.success("Thanks for trying the AI-generated app!")
`
}

function generateRequirements(code: string): string {
  const requirements = ["streamlit"]

  if (code.includes("pandas")) requirements.push("pandas")
  if (code.includes("matplotlib")) requirements.push("matplotlib")
  if (code.includes("numpy")) requirements.push("numpy")
  if (code.includes("plotly")) requirements.push("plotly")
  if (code.includes("seaborn")) requirements.push("seaborn")

  return requirements.join("\n")
}
