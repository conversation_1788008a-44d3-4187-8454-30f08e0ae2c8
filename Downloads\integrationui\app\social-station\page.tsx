import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { ArrowLeft, Calendar, Bot } from "lucide-react"
import Link from "next/link"

export default function SocialStationPage() {
  return (
    <div className="w-full h-screen bg-[#111111] flex flex-col">
      <header className="p-4 border-b border-gray-800 flex items-center justify-between bg-[#1C1C1C] z-10">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild className="border-gray-700 hover:bg-gray-800 bg-transparent">
            <Link href="/">
              <ArrowLeft className="w-4 h-4" />
              <span className="sr-only">Back to Chat</span>
            </Link>
          </Button>
          <h1 className="text-xl font-bold text-white">Social Station</h1>
        </div>
      </header>
      <main className="flex-1">
        <Tabs defaultValue="calendar" className="h-full flex flex-col">
          <TabsList className="bg-[#1C1C1C] border-b border-gray-800 h-auto justify-start px-6 rounded-none">
            <TabsTrigger
              value="calendar"
              className="text-gray-400 data-[state=active]:text-[#069494] data-[state=active]:shadow-[inset_0_-2px_0_0_#069494] rounded-none py-3 text-sm"
            >
              <Calendar className="w-4 h-4 mr-2" />
              Calendar
            </TabsTrigger>
            <TabsTrigger
              value="ai-influencer"
              className="text-gray-400 data-[state=active]:text-[#069494] data-[state=active]:shadow-[inset_0_-2px_0_0_#069494] rounded-none py-3 text-sm"
            >
              <Bot className="w-4 h-4 mr-2" />
              AI Influencer
            </TabsTrigger>
          </TabsList>
          <div className="flex-1 p-6">
            <TabsContent value="calendar">
              <div className="text-center text-gray-400">
                <p>Calendar content will be implemented here.</p>
              </div>
            </TabsContent>
            <TabsContent value="ai-influencer">
              <div className="text-center text-gray-400">
                <p>AI Influencer content will be implemented here.</p>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </main>
    </div>
  )
}
