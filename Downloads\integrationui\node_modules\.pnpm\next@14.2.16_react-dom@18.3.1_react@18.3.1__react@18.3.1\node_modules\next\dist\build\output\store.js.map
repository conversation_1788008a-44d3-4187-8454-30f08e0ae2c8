{"version": 3, "sources": ["../../../src/build/output/store.ts"], "names": ["formatTrigger", "store", "MAX_LOG_SKIP_DURATION", "internalSegments", "trigger", "segment", "includes", "replace", "length", "endsWith", "slice", "createStore", "appUrl", "bindAddr", "bootstrap", "lastStore", "hasStoreChanged", "nextStore", "Set", "Object", "keys", "every", "key", "is", "startTime", "triggerUrl", "undefined", "loadingLogTimer", "traceSpan", "subscribe", "state", "loading", "url", "trace", "setTimeout", "process", "env", "NEXT_TRIGGER_URL", "Log", "wait", "Date", "now", "errors", "error", "cleanError", "stripAnsi", "indexOf", "matches", "match", "prop", "split", "shift", "console", "log", "flushAllTraces", "teardownTraceSubscriber", "teardownHeapProfiler", "timeMessage", "time", "Math", "round", "modulesMessage", "totalModulesCount", "warnings", "warn", "join", "typeChecking", "info", "clearTimeout", "stop", "event"], "mappings": ";;;;;;;;;;;;;;;IA2BgBA,aAAa;eAAbA;;IAYHC,KAAK;eAALA;;;iEAvCW;kEACF;uBAC2B;qBACa;6DACzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMC,wBAAwB,IAAI,QAAQ;;AAoB1C,MAAMC,mBAAmB;IAAC;IAA0B;CAAoB;AACjE,SAASH,cAAcI,OAAe;IAC3C,KAAK,MAAMC,WAAWF,iBAAkB;QACtC,IAAIC,QAAQE,QAAQ,CAACD,UAAU;YAC7BD,UAAUA,QAAQG,OAAO,CAACF,SAAS;QACrC;IACF;IACA,IAAID,QAAQI,MAAM,GAAG,KAAKJ,QAAQK,QAAQ,CAAC,MAAM;QAC/CL,UAAUA,QAAQM,KAAK,CAAC,GAAG,CAAC;IAC9B;IACA,OAAON;AACT;AAEO,MAAMH,QAAQU,IAAAA,iBAAW,EAAc;IAC5CC,QAAQ;IACRC,UAAU;IACVC,WAAW;AACb;AAEA,IAAIC,YAAyB;IAAEH,QAAQ;IAAMC,UAAU;IAAMC,WAAW;AAAK;AAC7E,SAASE,gBAAgBC,SAAsB;IAC7C,IACE,AACE;WACK,IAAIC,IAAI;eAAIC,OAAOC,IAAI,CAACL;eAAeI,OAAOC,IAAI,CAACH;SAAW;KAClE,CACDI,KAAK,CAAC,CAACC,MAAQH,OAAOI,EAAE,CAACR,SAAS,CAACO,IAAI,EAAEL,SAAS,CAACK,IAAI,IACzD;QACA,OAAO;IACT;IAEAP,YAAYE;IACZ,OAAO;AACT;AAEA,IAAIO,YAAY;AAChB,IAAIpB,UAAU,GAAG,wCAAwC;;AACzD,IAAIqB,aAAiCC;AACrC,IAAIC,kBAAyC;AAC7C,IAAIC,YAAyB;AAE7B3B,MAAM4B,SAAS,CAAC,CAACC;IACf,IAAI,CAACd,gBAAgBc,QAAQ;QAC3B;IACF;IAEA,IAAIA,MAAMhB,SAAS,EAAE;QACnB;IACF;IAEA,IAAIgB,MAAMC,OAAO,EAAE;QACjB,IAAID,MAAM1B,OAAO,EAAE;YACjBA,UAAUJ,cAAc8B,MAAM1B,OAAO;YACrCqB,aAAaK,MAAME,GAAG;YACtB,IAAI5B,YAAY,WAAW;gBACzBwB,YAAYK,IAAAA,YAAK,EAAC,gBAAgBP,WAAW;oBAC3CtB,SAASA;gBACX;gBACA,IAAI,CAACuB,iBAAiB;oBACpB,8DAA8D;oBAC9DA,kBAAkBO,WAAW;wBAC3B,IACET,cACAA,eAAerB,WACf+B,QAAQC,GAAG,CAACC,gBAAgB,EAC5B;4BACAC,KAAIC,IAAI,CAAC,CAAC,UAAU,EAAEnC,QAAQ,EAAE,EAAEqB,WAAW,KAAK,CAAC;wBACrD,OAAO;4BACLa,KAAIC,IAAI,CAAC,CAAC,UAAU,EAAEnC,QAAQ,IAAI,CAAC;wBACrC;oBACF,GAAGF;gBACL;YACF;QACF;QACA,IAAIsB,cAAc,GAAG;YACnBA,YAAYgB,KAAKC,GAAG;QACtB;QACA;IACF;IAEA,IAAIX,MAAMY,MAAM,EAAE;QAChB,yBAAyB;QACzBJ,KAAIK,KAAK,CAACb,MAAMY,MAAM,CAAC,EAAE;QAEzB,MAAME,aAAaC,IAAAA,kBAAS,EAACf,MAAMY,MAAM,CAAC,EAAE;QAC5C,IAAIE,WAAWE,OAAO,CAAC,iBAAiB,CAAC,GAAG;YAC1C,MAAMC,UAAUH,WAAWI,KAAK,CAAC;YACjC,IAAID,SAAS;gBACX,KAAK,MAAMC,SAASD,QAAS;oBAC3B,MAAME,OAAO,AAACD,CAAAA,MAAME,KAAK,CAAC,KAAKC,KAAK,MAAM,EAAC,EAAGzC,KAAK,CAAC;oBACpD0C,QAAQC,GAAG,CACT,CAAC,iBAAiB,EAAEJ,KAAK,iDAAiD,EAAEA,KAAK,4DAA4D,CAAC;gBAElJ;gBACA;YACF;QACF;QACAzB,YAAY;QACZ,mEAAmE;QACnE8B,IAAAA,qBAAc;QACdC,IAAAA,4BAAuB;QACvBC,IAAAA,yBAAoB;QACpB;IACF;IAEA,IAAIC,cAAc;IAClB,IAAIjC,WAAW;QACb,MAAMkC,OAAOlB,KAAKC,GAAG,KAAKjB;QAC1BA,YAAY;QAEZiC,cACE,MACCC,CAAAA,OAAO,OAAO,CAAC,GAAG,EAAEC,KAAKC,KAAK,CAACF,OAAO,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAEA,KAAK,EAAE,CAAC,AAAD;IACvE;IAEA,IAAIG,iBAAiB;IACrB,IAAI/B,MAAMgC,iBAAiB,EAAE;QAC3BD,iBAAiB,CAAC,EAAE,EAAE/B,MAAMgC,iBAAiB,CAAC,SAAS,CAAC;IAC1D;IAEA,IAAIhC,MAAMiC,QAAQ,EAAE;QAClBzB,KAAI0B,IAAI,CAAClC,MAAMiC,QAAQ,CAACE,IAAI,CAAC;QAC7B,mEAAmE;QACnEX,IAAAA,qBAAc;QACdC,IAAAA,4BAAuB;QACvBC,IAAAA,yBAAoB;QACpB;IACF;IAEA,IAAI1B,MAAMoC,YAAY,EAAE;QACtB5B,KAAI6B,IAAI,CACN,CAAC,QAAQ,EAAE/D,QAAQ,EAAEqD,YAAY,EAAEI,eAAe,kBAAkB,CAAC;QAEvE;IACF;IAEA,IAAIzD,YAAY,WAAW;QACzBA,UAAU;IACZ,OAAO;QACL,IAAIuB,iBAAiB;YACnByC,aAAazC;YACbA,kBAAkB;QACpB;QACA,IAAIC,WAAW;YACbA,UAAUyC,IAAI;YACdzC,YAAY;QACd;QACAU,KAAIgC,KAAK,CACP,CAAC,QAAQ,EAAElE,UAAU,MAAMA,UAAU,GAAG,EAAEqD,YAAY,EAAEI,eAAe,CAAC;QAE1EzD,UAAU;IACZ;IAEA,mEAAmE;IACnEkD,IAAAA,qBAAc;IACdC,IAAAA,4BAAuB;IACvBC,IAAAA,yBAAoB;AACtB"}