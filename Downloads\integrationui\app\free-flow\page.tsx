"use client"

import type React from "react"
import { useRef, useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Brush, Eraser, Move, Square } from "lucide-react"
import Link from "next/link"
import { useTheme } from "next-themes"

export default function FreeFlowPage() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isDrawing, setIsDrawing] = useState(false)
  const [tool, setTool] = useState<"brush" | "eraser" | "move" | "shape">("brush")
  const { theme } = useTheme()

  const backgroundColor = theme === "dark" ? "#111111" : "#FFFFFF"
  const strokeColor = "#069494"

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    canvas.width = window.innerWidth
    canvas.height = window.innerHeight - 64 // Account for header

    ctx.fillStyle = backgroundColor
    ctx.fillRect(0, 0, canvas.width, canvas.height)
  }, [backgroundColor])

  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true)
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    ctx.beginPath()
    ctx.moveTo(x, y)
  }

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    ctx.lineWidth = tool === "eraser" ? 20 : 2
    ctx.lineCap = "round"
    ctx.strokeStyle = tool === "eraser" ? backgroundColor : strokeColor

    ctx.lineTo(x, y)
    ctx.stroke()
    ctx.beginPath()
    ctx.moveTo(x, y)
  }

  const stopDrawing = () => {
    setIsDrawing(false)
  }

  return (
    <div className="w-full h-screen bg-background flex flex-col">
      <header className="p-4 border-b flex items-center justify-between bg-card z-10">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/">
              <ArrowLeft className="w-4 h-4" />
              <span className="sr-only">Back to Chat</span>
            </Link>
          </Button>
          <h1 className="text-xl font-bold text-foreground">Free Flow</h1>
        </div>
        <div className="flex items-center gap-2 p-1 bg-secondary rounded-lg">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTool("brush")}
            className={
              tool === "brush"
                ? "bg-accent-primary text-primary-foreground hover:bg-accent-secondary"
                : "hover:bg-accent"
            }
          >
            <Brush className="w-5 h-5" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTool("eraser")}
            className={
              tool === "eraser"
                ? "bg-accent-primary text-primary-foreground hover:bg-accent-secondary"
                : "hover:bg-accent"
            }
          >
            <Eraser className="w-5 h-5" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTool("move")}
            className={
              tool === "move"
                ? "bg-accent-primary text-primary-foreground hover:bg-accent-secondary"
                : "hover:bg-accent"
            }
          >
            <Move className="w-5 h-5" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTool("shape")}
            className={
              tool === "shape"
                ? "bg-accent-primary text-primary-foreground hover:bg-accent-secondary"
                : "hover:bg-accent"
            }
          >
            <Square className="w-5 h-5" />
          </Button>
        </div>
      </header>
      <main className="flex-1 relative">
        <canvas
          ref={canvasRef}
          className="absolute inset-0 cursor-crosshair"
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseLeave={stopDrawing}
        />
      </main>
    </div>
  )
}
