"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { PanelRightClose, Code, Eye, Terminal, LoaderCircle } from "lucide-react"
import type { ReplitState, ReplitFile } from "@/lib/types"
import ReplitPreview from "./replit-preview"
import ReplitTerminal from "./replit-terminal"
import ReplitFileTree from "./replit-file-tree"
import ReplitCodeViewer from "./replit-code-viewer"

type SecondaryPanelProps = {
  togglePanel: () => void
  replitId: string | null
}

export default function SecondaryPanel({ togglePanel, replitId }: SecondaryPanelProps) {
  const [state, setState] = useState<ReplitState | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedFile, setSelectedFile] = useState<ReplitFile | null>(null)

  useEffect(() => {
    const fetchState = async () => {
      if (!replitId) return
      setLoading(true)
      setError(null)
      try {
        const response = await fetch(`/api/replit/state?id=${replitId}`)
        if (!response.ok) throw new Error("Failed to fetch Replit state")
        const data: ReplitState = await response.json()
        setState(data)

        // Find and select the first file by default
        const firstFile = findFirstFile(data.files)
        setSelectedFile(firstFile)
      } catch (e: any) {
        setError(e.message)
      } finally {
        setLoading(false)
      }
    }
    fetchState()
  }, [replitId])

  const findFirstFile = (files: ReplitFile[]): ReplitFile | null => {
    for (const file of files) {
      if (file.type === "file") return file
      if (file.type === "directory" && file.children) {
        const found = findFirstFile(file.children)
        if (found) return found
      }
    }
    return null
  }

  if (!replitId) {
    return (
      <aside className="w-1/2 flex flex-col items-center justify-center bg-card border-l p-4 text-center">
        <Terminal className="w-12 h-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold text-foreground">No Active Replit</h3>
        <p className="text-muted-foreground text-sm">
          Ask the agent to create a presentation, data analysis, or web app to start a new Replit session.
        </p>
      </aside>
    )
  }

  if (loading) {
    return (
      <aside className="w-1/2 flex flex-col items-center justify-center bg-card border-l">
        <LoaderCircle className="w-8 h-8 animate-spin text-muted-foreground" />
        <p className="mt-4 text-muted-foreground">Initializing Replit...</p>
      </aside>
    )
  }

  if (error || !state) {
    return (
      <aside className="w-1/2 flex flex-col items-center justify-center bg-card border-l text-destructive">
        <p>Error loading Replit: {error}</p>
      </aside>
    )
  }

  return (
    <aside className="w-1/2 flex flex-col bg-card border-l relative transition-all duration-300 ease-in-out">
      <Button
        variant="ghost"
        size="icon"
        onClick={togglePanel}
        className="absolute top-2 left-2 z-10 text-muted-foreground hover:bg-secondary hover:text-foreground h-8 w-8"
        title="Collapse panel"
      >
        <PanelRightClose className="w-5 h-5" />
      </Button>
      <Tabs defaultValue="preview" className="flex flex-col h-full">
        <TabsList className="grid w-full grid-cols-3 bg-card border-b rounded-none h-14">
          <TabsTrigger
            value="preview"
            className="data-[state=active]:bg-secondary data-[state=active]:text-foreground text-muted-foreground"
          >
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </TabsTrigger>
          <TabsTrigger
            value="code"
            className="data-[state=active]:bg-secondary data-[state=active]:text-foreground text-muted-foreground"
          >
            <Code className="w-4 h-4 mr-2" />
            Code
          </TabsTrigger>
          <TabsTrigger
            value="terminal"
            className="data-[state=active]:bg-secondary data-[state=active]:text-foreground text-muted-foreground"
          >
            <Terminal className="w-4 h-4 mr-2" />
            Terminal
          </TabsTrigger>
        </TabsList>

        <TabsContent value="preview" className="flex-1 mt-0">
          <ReplitPreview url={state.previewUrl} />
        </TabsContent>

        <TabsContent value="code" className="flex-1 flex mt-0 overflow-hidden">
          <div className="w-1/3 border-r overflow-y-auto">
            <ReplitFileTree files={state.files} onSelectFile={setSelectedFile} selectedFile={selectedFile} />
          </div>
          <div className="w-2/3 overflow-y-auto">
            <ReplitCodeViewer file={selectedFile} />
          </div>
        </TabsContent>

        <TabsContent value="terminal" className="flex-1 mt-0">
          <ReplitTerminal logs={state.terminalOutput} />
        </TabsContent>
      </Tabs>
    </aside>
  )
}
