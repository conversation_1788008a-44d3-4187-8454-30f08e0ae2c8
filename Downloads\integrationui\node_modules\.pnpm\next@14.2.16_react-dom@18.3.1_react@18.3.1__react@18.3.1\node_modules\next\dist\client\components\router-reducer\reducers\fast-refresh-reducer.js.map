{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/fast-refresh-reducer.ts"], "names": ["fastRefreshReducer", "fastRefreshReducerImpl", "state", "action", "origin", "mutable", "href", "canonicalUrl", "preserveCustomHistoryState", "cache", "createEmptyCacheNode", "includeNextUrl", "hasInterceptionRouteInCurrentTree", "tree", "lazyData", "fetchServerResponse", "URL", "nextUrl", "buildId", "then", "flightData", "canonicalUrlOverride", "handleExternalUrl", "pushRef", "pendingPush", "currentTree", "currentCache", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "applyRouterStatePatchToTree", "handleSegmentMismatch", "isNavigatingToNewRootLayout", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "applied", "applyFlightData", "patchedTree", "handleMutable", "fastRefreshReducerNoop", "_action", "process", "env", "NODE_ENV"], "mappings": ";;;;+BA4HaA;;;eAAAA;;;qCA5HuB;mCACF;6CACU;6CACA;iCAOV;+BACJ;iCACE;2BAEK;uCACC;mDACY;AAElD,wFAAwF;AACxF,SAASC,uBACPC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EAAEC,MAAM,EAAE,GAAGD;IACnB,MAAME,UAAmB,CAAC;IAC1B,MAAMC,OAAOJ,MAAMK,YAAY;IAE/BF,QAAQG,0BAA0B,GAAG;IAErC,MAAMC,QAAmBC,IAAAA,+BAAoB;IAC7C,sFAAsF;IACtF,sHAAsH;IACtH,MAAMC,iBAAiBC,IAAAA,oEAAiC,EAACV,MAAMW,IAAI;IAEnE,uDAAuD;IACvD,wCAAwC;IACxCJ,MAAMK,QAAQ,GAAGC,IAAAA,wCAAmB,EAClC,IAAIC,IAAIV,MAAMF,SACd;QAACF,MAAMW,IAAI,CAAC,EAAE;QAAEX,MAAMW,IAAI,CAAC,EAAE;QAAEX,MAAMW,IAAI,CAAC,EAAE;QAAE;KAAU,EACxDF,iBAAiBT,MAAMe,OAAO,GAAG,MACjCf,MAAMgB,OAAO;IAGf,OAAOT,MAAMK,QAAQ,CAACK,IAAI,CACxB;YAAC,CAACC,YAAYC,qBAAqB;QACjC,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOE,IAAAA,kCAAiB,EACtBpB,OACAG,SACAe,YACAlB,MAAMqB,OAAO,CAACC,WAAW;QAE7B;QAEA,+DAA+D;QAC/Df,MAAMK,QAAQ,GAAG;QAEjB,IAAIW,cAAcvB,MAAMW,IAAI;QAC5B,IAAIa,eAAexB,MAAMO,KAAK;QAE9B,KAAK,MAAMkB,kBAAkBP,WAAY;YACvC,oFAAoF;YACpF,IAAIO,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAO5B;YACT;YAEA,mGAAmG;YACnG,MAAM,CAAC6B,UAAU,GAAGJ;YACpB,MAAMK,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJR,aACAM,WACA7B,MAAMK,YAAY;YAGpB,IAAIyB,YAAY,MAAM;gBACpB,OAAOE,IAAAA,4CAAqB,EAAChC,OAAOC,QAAQ4B;YAC9C;YAEA,IAAII,IAAAA,wDAA2B,EAACV,aAAaO,UAAU;gBACrD,OAAOV,IAAAA,kCAAiB,EACtBpB,OACAG,SACAC,MACAJ,MAAMqB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMY,2BAA2Bf,uBAC7BgB,IAAAA,oCAAiB,EAAChB,wBAClBiB;YAEJ,IAAIjB,sBAAsB;gBACxBhB,QAAQE,YAAY,GAAG6B;YACzB;YACA,MAAMG,UAAUC,IAAAA,gCAAe,EAACd,cAAcjB,OAAOkB;YAErD,IAAIY,SAAS;gBACXlC,QAAQI,KAAK,GAAGA;gBAChBiB,eAAejB;YACjB;YAEAJ,QAAQoC,WAAW,GAAGT;YACtB3B,QAAQE,YAAY,GAAGD;YAEvBmB,cAAcO;QAChB;QACA,OAAOU,IAAAA,4BAAa,EAACxC,OAAOG;IAC9B,GACA,IAAMH;AAEV;AAEA,SAASyC,uBACPzC,KAA2B,EAC3B0C,OAA0B;IAE1B,OAAO1C;AACT;AAEO,MAAMF,qBACX6C,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrBJ,yBACA1C"}