import { NextResponse } from "next/server"
import { Sandbox } from "e2b"
import type { SandboxState } from "@/lib/types"

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const sandboxId = searchParams.get("id")

  if (!sandboxId) {
    return NextResponse.json({ error: "Sandbox ID is required" }, { status: 400 })
  }

  try {
    // Connect to existing sandbox
    const sandbox = await Sandbox.connect(sandboxId, {
      apiKey: process.env.E2B_API_KEY,
    })

    // Get Docker container status
    const dockerPs = await sandbox.commands.run("docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'")
    const dockerImages = await sandbox.commands.run(
      "docker images --format 'table {{.Repository}}\t{{.Tag}}\t{{.Size}}'",
    )

    // Get file system structure
    const lsResult = await sandbox.commands.run(
      "find /home -type f -name '*.js' -o -name '*.ts' -o -name '*.json' -o -name '*.html' | head -20",
    )

    // Build file system from actual sandbox
    const fileSystem = await buildFileSystemFromSandbox(sandbox)

    const state: SandboxState = {
      sandboxId,
      status: "running",
      previewUrl: `https://${sandboxId}.e2b.dev`, // E2B provides preview URLs
      terminalOutput: [
        "Docker environment initialized",
        dockerPs.stdout || "No containers running",
        dockerImages.stdout || "No images found",
        lsResult.stdout || "No files found",
        "Ready for Docker commands...",
      ],
      fileSystem,
    }

    return NextResponse.json(state)
  } catch (error) {
    console.error("Failed to get sandbox state:", error)
    return NextResponse.json({ error: "Failed to get sandbox state" }, { status: 500 })
  }
}

async function buildFileSystemFromSandbox(sandbox: any) {
  try {
    // Get basic file structure
    const result = await sandbox.commands.run("ls -la /home")

    return [
      {
        id: "1",
        name: "home",
        type: "directory" as const,
        children: [
          {
            id: "2",
            name: "Dockerfile",
            type: "file" as const,
            content: "# Docker configuration will be loaded from sandbox",
          },
          {
            id: "3",
            name: "docker-compose.yml",
            type: "file" as const,
            content: "# Docker compose configuration",
          },
        ],
      },
    ]
  } catch (error) {
    console.error("Failed to build file system:", error)
    return []
  }
}
