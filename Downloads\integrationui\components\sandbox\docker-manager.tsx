"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import {
  Play,
  Square,
  Trash2,
  FileText,
  RefreshCw,
  Container,
  RotateCcw,
  Pause,
  PlayCircle,
  Info,
  Activity,
  Settings,
  Download,
  Search,
  HardDrive,
  ImageIcon,
} from "lucide-react"

type DockerContainer = {
  name: string
  image: string
  status: string
  ports: string
  created?: string
}

type DockerImage = {
  repository: string
  tag: string
  id: string
  size: string
  created?: string
}

type ContainerStats = {
  cpu: string
  memory: string
  network: string
  blockIO: string
}

type DockerManagerProps = {
  sandboxId: string | null
}

export default function DockerManager({ sandboxId }: DockerManagerProps) {
  const [containers, setContainers] = useState<DockerContainer[]>([])
  const [images, setImages] = useState<DockerImage[]>([])
  const [loading, setLoading] = useState(false)
  const [newImage, setNewImage] = useState("")
  const [newContainerName, setNewContainerName] = useState("")
  const [newPorts, setNewPorts] = useState("")
  const [newEnvVars, setNewEnvVars] = useState("")
  const [pullImage, setPullImage] = useState("")
  const [searchTerm, setSearchTerm] = useState("")
  const [searchResults, setSearchResults] = useState("")
  const [logs, setLogs] = useState<Record<string, string>>({})
  const [stats, setStats] = useState<Record<string, ContainerStats>>({})
  const [inspectData, setInspectData] = useState<Record<string, string>>({})
  const [systemInfo, setSystemInfo] = useState("")

  const executeDockerCommand = async (command: string, containerName?: string, image?: string, options?: any) => {
    if (!sandboxId) return

    setLoading(true)
    try {
      const response = await fetch("/api/e2b/docker", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          sandboxId,
          command,
          containerName,
          image,
          options,
        }),
      })

      const result = await response.json()

      if (command === "ps") {
        parseContainerList(result.output)
      } else if (command === "images") {
        parseImageList(result.output)
      } else if (command === "logs" && containerName) {
        setLogs((prev) => ({ ...prev, [containerName]: result.output }))
      } else if (command === "stats" && containerName) {
        parseContainerStats(containerName, result.output)
      } else if (command === "inspect" && containerName) {
        setInspectData((prev) => ({ ...prev, [containerName]: result.output }))
      } else if (command === "image-inspect" && image) {
        setInspectData((prev) => ({ ...prev, [image]: result.output }))
      } else if (command === "search") {
        setSearchResults(result.output)
      } else if (command === "system-df") {
        setSystemInfo(result.output)
      }

      return result
    } catch (error) {
      console.error("Docker command failed:", error)
    } finally {
      setLoading(false)
    }
  }

  const parseContainerList = (output: string) => {
    if (!output) return

    const lines = output.split("\n").slice(1) // Skip header
    const parsed = lines
      .filter((line) => line.trim())
      .map((line) => {
        const parts = line.split("\t")
        return {
          name: parts[0] || "unknown",
          image: parts[1] || "unknown",
          status: parts[2] || "unknown",
          ports: parts[3] || "none",
          created: parts[4] || "unknown",
        }
      })

    setContainers(parsed)
  }

  const parseImageList = (output: string) => {
    if (!output) return

    const lines = output.split("\n").slice(1) // Skip header
    const parsed = lines
      .filter((line) => line.trim())
      .map((line) => {
        const parts = line.split("\t")
        return {
          repository: parts[0] || "unknown",
          tag: parts[1] || "unknown",
          id: parts[2] || "unknown",
          size: parts[3] || "unknown",
          created: parts[4] || "unknown",
        }
      })

    setImages(parsed)
  }

  const parseContainerStats = (containerName: string, output: string) => {
    if (!output) return

    const lines = output.split("\n").slice(1) // Skip header
    const line = lines[0]
    if (line) {
      const parts = line.split("\t")
      setStats((prev) => ({
        ...prev,
        [containerName]: {
          cpu: parts[1] || "0%",
          memory: parts[2] || "0B / 0B",
          network: parts[3] || "0B / 0B",
          blockIO: parts[4] || "0B / 0B",
        },
      }))
    }
  }

  const refreshContainers = () => {
    executeDockerCommand("ps")
  }

  const refreshImages = () => {
    executeDockerCommand("images")
  }

  const pullDockerImage = async () => {
    if (!pullImage.trim()) return
    await executeDockerCommand("pull", undefined, pullImage)
    setPullImage("")
    refreshImages()
  }

  const removeImage = async (imageId: string) => {
    await executeDockerCommand("rmi", undefined, imageId)
    refreshImages()
  }

  const searchImages = async () => {
    if (!searchTerm.trim()) return
    await executeDockerCommand("search", undefined, searchTerm)
  }

  const inspectImage = async (imageId: string) => {
    await executeDockerCommand("image-inspect", undefined, imageId)
  }

  const pruneImages = async () => {
    await executeDockerCommand("prune-images")
    refreshImages()
  }

  const getSystemInfo = () => {
    executeDockerCommand("system-df")
  }

  const runContainer = async () => {
    if (!newImage.trim()) return

    const containerName = newContainerName.trim() || `container-${Date.now()}`
    const options = {
      ports: newPorts.trim() || undefined,
      env: newEnvVars.trim() ? newEnvVars.split("\n").filter((env) => env.trim()) : undefined,
    }

    await executeDockerCommand("run", containerName, newImage, options)
    setNewImage("")
    setNewContainerName("")
    setNewPorts("")
    setNewEnvVars("")
    refreshContainers()
  }

  const startContainer = async (name: string) => {
    await executeDockerCommand("start", name)
    refreshContainers()
  }

  const restartContainer = async (name: string) => {
    await executeDockerCommand("restart", name)
    refreshContainers()
  }

  const pauseContainer = async (name: string) => {
    await executeDockerCommand("pause", name)
    refreshContainers()
  }

  const unpauseContainer = async (name: string) => {
    await executeDockerCommand("unpause", name)
    refreshContainers()
  }

  const stopContainer = async (name: string) => {
    await executeDockerCommand("stop", name)
    refreshContainers()
  }

  const removeContainer = async (name: string) => {
    await executeDockerCommand("remove", name)
    refreshContainers()
  }

  const viewLogs = async (name: string) => {
    await executeDockerCommand("logs", name)
  }

  const viewStats = async (name: string) => {
    await executeDockerCommand("stats", name)
  }

  const inspectContainer = async (name: string) => {
    await executeDockerCommand("inspect", name)
  }

  useEffect(() => {
    if (sandboxId) {
      refreshContainers()
      refreshImages()
    }
  }, [sandboxId])

  if (!sandboxId) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        <div className="text-center">
          <Container className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No active sandbox</p>
          <p className="text-sm">Start a sandbox to manage Docker containers</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-background">
      <Tabs defaultValue="containers" className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="containers">Containers</TabsTrigger>
          <TabsTrigger value="images">Images</TabsTrigger>
          <TabsTrigger value="run">Run New</TabsTrigger>
        </TabsList>

        <TabsContent value="containers" className="flex-1 p-4 space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Container Management</h3>
            <Button onClick={refreshContainers} disabled={loading} size="sm" variant="outline">
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`} />
              Refresh
            </Button>
          </div>

          <div className="space-y-2">
            {containers.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center text-muted-foreground">
                  <Container className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No containers found</p>
                </CardContent>
              </Card>
            ) : (
              containers.map((container, index) => (
                <Card key={index}>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">{container.name}</CardTitle>
                      <Badge variant={container.status.includes("Up") ? "default" : "secondary"}>
                        {container.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Image: {container.image} | Ports: {container.ports}
                    </p>
                    {container.created && <p className="text-xs text-muted-foreground">Created: {container.created}</p>}
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex flex-wrap gap-2 mb-3">
                      {!container.status.includes("Up") && (
                        <Button
                          onClick={() => startContainer(container.name)}
                          size="sm"
                          variant="outline"
                          disabled={loading}
                        >
                          <PlayCircle className="w-4 h-4 mr-1" />
                          Start
                        </Button>
                      )}

                      {container.status.includes("Up") && (
                        <>
                          <Button
                            onClick={() => restartContainer(container.name)}
                            size="sm"
                            variant="outline"
                            disabled={loading}
                          >
                            <RotateCcw className="w-4 h-4 mr-1" />
                            Restart
                          </Button>

                          {!container.status.includes("Paused") ? (
                            <Button
                              onClick={() => pauseContainer(container.name)}
                              size="sm"
                              variant="outline"
                              disabled={loading}
                            >
                              <Pause className="w-4 h-4 mr-1" />
                              Pause
                            </Button>
                          ) : (
                            <Button
                              onClick={() => unpauseContainer(container.name)}
                              size="sm"
                              variant="outline"
                              disabled={loading}
                            >
                              <Play className="w-4 h-4 mr-1" />
                              Unpause
                            </Button>
                          )}

                          <Button
                            onClick={() => stopContainer(container.name)}
                            size="sm"
                            variant="outline"
                            disabled={loading}
                          >
                            <Square className="w-4 h-4 mr-1" />
                            Stop
                          </Button>
                        </>
                      )}

                      <Button
                        onClick={() => removeContainer(container.name)}
                        size="sm"
                        variant="outline"
                        disabled={loading}
                      >
                        <Trash2 className="w-4 h-4 mr-1" />
                        Remove
                      </Button>
                    </div>

                    <div className="flex flex-wrap gap-2">
                      <Button onClick={() => viewLogs(container.name)} size="sm" variant="outline" disabled={loading}>
                        <FileText className="w-4 h-4 mr-1" />
                        Logs
                      </Button>

                      <Button onClick={() => viewStats(container.name)} size="sm" variant="outline" disabled={loading}>
                        <Activity className="w-4 h-4 mr-1" />
                        Stats
                      </Button>

                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            onClick={() => inspectContainer(container.name)}
                            size="sm"
                            variant="outline"
                            disabled={loading}
                          >
                            <Info className="w-4 h-4 mr-1" />
                            Inspect
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle>Container Inspection: {container.name}</DialogTitle>
                          </DialogHeader>
                          <Textarea
                            value={inspectData[container.name] || "Click 'Inspect' to load container details..."}
                            readOnly
                            className="min-h-[400px] font-mono text-xs"
                          />
                        </DialogContent>
                      </Dialog>
                    </div>

                    {stats[container.name] && (
                      <div className="mt-3 p-2 bg-muted rounded text-xs">
                        <div className="grid grid-cols-2 gap-2">
                          <div>CPU: {stats[container.name].cpu}</div>
                          <div>Memory: {stats[container.name].memory}</div>
                          <div>Network: {stats[container.name].network}</div>
                          <div>Block I/O: {stats[container.name].blockIO}</div>
                        </div>
                      </div>
                    )}

                    {logs[container.name] && (
                      <div className="mt-3 p-2 bg-muted rounded text-xs font-mono max-h-32 overflow-y-auto">
                        <pre className="whitespace-pre-wrap">{logs[container.name]}</pre>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="images" className="flex-1 p-4 space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Image Management</h3>
            <div className="flex gap-2">
              <Button onClick={getSystemInfo} size="sm" variant="outline" disabled={loading}>
                <HardDrive className="w-4 h-4 mr-2" />
                System Info
              </Button>
              <Button onClick={refreshImages} disabled={loading} size="sm" variant="outline">
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`} />
                Refresh
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center">
                  <Download className="w-4 h-4 mr-2" />
                  Pull Image
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Input
                  placeholder="e.g., nginx:latest, ubuntu:20.04"
                  value={pullImage}
                  onChange={(e) => setPullImage(e.target.value)}
                />
                <Button onClick={pullDockerImage} disabled={loading || !pullImage.trim()} className="w-full" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Pull Image
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center">
                  <Search className="w-4 h-4 mr-2" />
                  Search Images
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Input
                  placeholder="Search Docker Hub..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <Button onClick={searchImages} disabled={loading || !searchTerm.trim()} className="w-full" size="sm">
                  <Search className="w-4 h-4 mr-2" />
                  Search
                </Button>
              </CardContent>
            </Card>
          </div>

          {systemInfo && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">System Usage</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs font-mono bg-muted p-2 rounded overflow-x-auto">{systemInfo}</pre>
              </CardContent>
            </Card>
          )}

          {searchResults && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Search Results</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs font-mono bg-muted p-2 rounded max-h-40 overflow-y-auto">{searchResults}</pre>
              </CardContent>
            </Card>
          )}

          <div className="flex items-center justify-between">
            <h4 className="font-medium">Available Images</h4>
            <Button onClick={pruneImages} size="sm" variant="outline" disabled={loading}>
              <Trash2 className="w-4 h-4 mr-1" />
              Prune Unused
            </Button>
          </div>

          <div className="space-y-2">
            {images.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center text-muted-foreground">
                  <ImageIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No images found</p>
                </CardContent>
              </Card>
            ) : (
              images.map((image, index) => (
                <Card key={index}>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">
                        {image.repository}:{image.tag}
                      </CardTitle>
                      <Badge variant="outline">{image.size}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      ID: {image.id.substring(0, 12)}... | Created: {image.created}
                    </p>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex gap-2">
                      <Button
                        onClick={() => setNewImage(`${image.repository}:${image.tag}`)}
                        size="sm"
                        variant="outline"
                      >
                        <Play className="w-4 h-4 mr-1" />
                        Use
                      </Button>
                      <Button onClick={() => removeImage(image.id)} size="sm" variant="outline" disabled={loading}>
                        <Trash2 className="w-4 h-4 mr-1" />
                        Remove
                      </Button>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button onClick={() => inspectImage(image.id)} size="sm" variant="outline" disabled={loading}>
                            <Info className="w-4 h-4 mr-1" />
                            Inspect
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle>
                              Image Inspection: {image.repository}:{image.tag}
                            </DialogTitle>
                          </DialogHeader>
                          <Textarea
                            value={inspectData[image.id] || "Click 'Inspect' to load image details..."}
                            readOnly
                            className="min-h-[400px] font-mono text-xs"
                          />
                        </DialogContent>
                      </Dialog>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="run" className="flex-1 p-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                Run New Container
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Docker Image</label>
                <Input
                  placeholder="e.g., nginx:latest, node:18, python:3.9"
                  value={newImage}
                  onChange={(e) => setNewImage(e.target.value)}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Container Name (optional)</label>
                <Input
                  placeholder="Auto-generated if empty"
                  value={newContainerName}
                  onChange={(e) => setNewContainerName(e.target.value)}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Port Mapping (optional)</label>
                <Input
                  placeholder="e.g., 8080:80 or 3000:3000"
                  value={newPorts}
                  onChange={(e) => setNewPorts(e.target.value)}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Environment Variables (optional)</label>
                <Textarea
                  placeholder="One per line, e.g.:&#10;NODE_ENV=production&#10;API_KEY=your-key"
                  value={newEnvVars}
                  onChange={(e) => setNewEnvVars(e.target.value)}
                  rows={3}
                />
              </div>
              <Button onClick={runContainer} disabled={loading || !newImage.trim()} className="w-full">
                <Play className="w-4 h-4 mr-2" />
                Run Container
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
