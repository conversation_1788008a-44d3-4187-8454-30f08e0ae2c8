{"version": 3, "sources": ["../../../src/build/webpack/utils.ts"], "names": ["forEachEntryModule", "formatBarrelOptimizedResource", "getModuleReferencesInOrder", "traverseModules", "compilation", "callback", "filterChunkGroup", "chunkGroups", "for<PERSON>ach", "chunkGroup", "chunks", "chunk", "chunkModules", "chunkGraph", "getChunkModulesIterable", "mod", "modId", "getModuleId", "toString", "anyModule", "modules", "subMod", "name", "entry", "entries", "startsWith", "isAppRouteRoute", "entryDependency", "dependencies", "request", "entryModule", "moduleGraph", "getResolvedModule", "dependency", "modRequest", "includes", "resource", "matchResource", "module", "connections", "connection", "getOutgoingConnections", "push", "index", "getParentBlockIndex", "sort", "a", "b", "map", "c"], "mappings": ";;;;;;;;;;;;;;;;;IA4CgBA,kBAAkB;eAAlBA;;IA2CAC,6BAA6B;eAA7BA;;IAOAC,0BAA0B;eAA1BA;;IAnFAC,eAAe;eAAfA;;;iCAHgB;AAGzB,SAASA,gBACdC,WAAwB,EACxBC,QAKQ,EACRC,gBAAsD;IAEtDF,YAAYG,WAAW,CAACC,OAAO,CAAC,CAACC;QAC/B,IAAIH,oBAAoB,CAACA,iBAAiBG,aAAa;YACrD;QACF;QACAA,WAAWC,MAAM,CAACF,OAAO,CAAC,CAACG;YACzB,MAAMC,eAAeR,YAAYS,UAAU,CAACC,uBAAuB,CACjEH;YAGF,KAAK,MAAMI,OAAOH,aAAc;oBAChBR;gBAAd,MAAMY,SAAQZ,sCAAAA,YAAYS,UAAU,CAACI,WAAW,CAACF,yBAAnCX,oCAAyCc,QAAQ;gBAC/Db,SAASU,KAAKJ,OAAOF,YAAYO;gBACjC,MAAMG,YAAYJ;gBAClB,IAAII,UAAUC,OAAO,EAAE;oBACrB,KAAK,MAAMC,UAAUF,UAAUC,OAAO,CACpCf,SAASgB,QAAQV,OAAOF,YAAYO;gBACxC;YACF;QACF;IACF;AACF;AAGO,SAAShB,mBACdI,WAAgB,EAChBC,QAA6E;IAE7E,KAAK,MAAM,CAACiB,MAAMC,MAAM,IAAInB,YAAYoB,OAAO,CAACA,OAAO,GAAI;YAWPD;QAVlD,gCAAgC;QAChC,IACED,KAAKG,UAAU,CAAC,aAChB,4BAA4B;QAC3BH,KAAKG,UAAU,CAAC,WAAWC,IAAAA,gCAAe,EAACJ,OAC5C;YACA;QACF;QAEA,wDAAwD;QACxD,MAAMK,mBAA4CJ,sBAAAA,MAAMK,YAAY,qBAAlBL,mBAAoB,CAAC,EAAE;QACzE,mDAAmD;QACnD,IAAI,CAACI,mBAAmB,CAACA,gBAAgBE,OAAO,EAAE;QAElD,MAAMA,UAAUF,gBAAgBE,OAAO;QAEvC,IACE,CAACA,QAAQJ,UAAU,CAAC,4BACpB,CAACI,QAAQJ,UAAU,CAAC,qBAEpB;QAEF,IAAIK,cACF1B,YAAY2B,WAAW,CAACC,iBAAiB,CAACL;QAE5C,IAAIE,QAAQJ,UAAU,CAAC,0BAA0B;YAC/CK,YAAYF,YAAY,CAACpB,OAAO,CAAC,CAACyB;gBAChC,MAAMC,aAAiC,AAACD,WAAmBJ,OAAO;gBAClE,IAAIK,8BAAAA,WAAYC,QAAQ,CAAC,oBAAoB;oBAC3CL,cAAc1B,YAAY2B,WAAW,CAACC,iBAAiB,CAACC;gBAC1D;YACF;QACF;QAEA5B,SAAS;YAAEiB;YAAMQ;QAAY;IAC/B;AACF;AAEO,SAAS7B,8BACdmC,QAAgB,EAChBC,aAAqB;IAErB,OAAO,CAAC,EAAED,SAAS,CAAC,EAAEC,cAAc,CAAC;AACvC;AAEO,SAASnC,2BACdoC,OAAc,EACdP,WAAwB;IAExB,MAAMQ,cAAc,EAAE;IACtB,KAAK,MAAMC,cAAcT,YAAYU,sBAAsB,CAACH,SAAS;QACnE,IAAIE,WAAWP,UAAU,IAAIO,WAAWF,MAAM,EAAE;YAC9CC,YAAYG,IAAI,CAAC;gBACfF;gBACAG,OAAOZ,YAAYa,mBAAmB,CAACJ,WAAWP,UAAU;YAC9D;QACF;IACF;IACAM,YAAYM,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEH,KAAK,GAAGI,EAAEJ,KAAK;IAC5C,OAAOJ,YAAYS,GAAG,CAAC,CAACC,IAAMA,EAAET,UAAU;AAC5C"}