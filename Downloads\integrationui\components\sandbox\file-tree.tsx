"use client"

import { useState } from "react"
import { Folder, File, ChevronRight, ChevronDown } from "lucide-react"
import type { FileSystemNode } from "@/lib/types"
import { cn } from "@/lib/utils"

type FileTreeProps = {
  nodes: FileSystemNode[]
  onSelectFile: (file: FileSystemNode) => void
  selectedFile: FileSystemNode | null
}

const Node = ({
  node,
  onSelectFile,
  selectedFile,
}: {
  node: FileSystemNode
  onSelectFile: (file: FileSystemNode) => void
  selectedFile: FileSystemNode | null
}) => {
  const [isOpen, setIsOpen] = useState(true)

  const isSelected = selectedFile?.id === node.id

  if (node.type === "directory") {
    return (
      <div className="pl-4">
        <div
          className="flex items-center cursor-pointer py-1 px-2 rounded hover:bg-secondary"
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? <ChevronDown className="w-4 h-4 mr-1" /> : <ChevronRight className="w-4 h-4 mr-1" />}
          <Folder className="w-4 h-4 mr-2 text-yellow-500" />
          <span>{node.name}</span>
        </div>
        {isOpen && node.children && (
          <div className="border-l border-border ml-2">
            {node.children.map((child) => (
              <Node key={child.id} node={child} onSelectFile={onSelectFile} selectedFile={selectedFile} />
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="pl-4">
      <div
        className={cn(
          "flex items-center cursor-pointer py-1 px-2 rounded hover:bg-secondary",
          isSelected && "bg-accent-primary/20",
        )}
        onClick={() => onSelectFile(node)}
      >
        <File className="w-4 h-4 mr-2 text-blue-500" />
        <span>{node.name}</span>
      </div>
    </div>
  )
}

export default function FileTree({ nodes, onSelectFile, selectedFile }: FileTreeProps) {
  return (
    <div className="p-2 text-sm">
      {nodes.map((node) => (
        <Node key={node.id} node={node} onSelectFile={onSelectFile} selectedFile={selectedFile} />
      ))}
    </div>
  )
}
