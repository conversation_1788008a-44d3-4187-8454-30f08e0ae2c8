import { Textarea } from "@/components/ui/textarea"
import type { FileSystemNode } from "@/lib/types"

type CodeViewerProps = {
  file: FileSystemNode | null
}

export default function CodeViewer({ file }: CodeViewerProps) {
  if (!file) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        <p>Select a file to view its content</p>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-background">
      <div className="p-2 border-b text-sm text-muted-foreground">{file.name}</div>
      <Textarea
        value={file.content || "/* This file is empty. */"}
        readOnly
        className="flex-1 bg-secondary border-none font-mono text-sm resize-none rounded-none focus-visible:ring-0"
      />
    </div>
  )
}
