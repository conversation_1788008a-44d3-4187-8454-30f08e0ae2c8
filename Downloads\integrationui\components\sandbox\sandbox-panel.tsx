"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { PanelRightClose, Code, Eye, Terminal, Container, LoaderCircle } from "lucide-react"
import type { SandboxState, FileSystemNode } from "@/lib/types"
import SandboxPreview from "./sandbox-preview"
import VirtualMachine from "./virtual-machine"
import FileTree from "./file-tree"
import CodeViewer from "./code-viewer"
import DockerManager from "./docker-manager"

type SandboxPanelProps = {
  togglePanel: () => void
  sandboxId: string | null // Changed from string
}

export default function SandboxPanel({ togglePanel, sandboxId }: SandboxPanelProps) {
  const [state, setState] = useState<SandboxState | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedFile, setSelectedFile] = useState<FileSystemNode | null>(null)

  useEffect(() => {
    const fetchState = async () => {
      if (!sandboxId) return
      setLoading(true)
      setError(null)
      try {
        const response = await fetch(`/api/e2b/state?id=${sandboxId}`)
        if (!response.ok) throw new Error("Failed to fetch sandbox state")
        const data: SandboxState = await response.json()
        setState(data)

        // Find and select the first file by default
        const firstFile = findFirstFile(data.fileSystem)
        setSelectedFile(firstFile)
      } catch (e: any) {
        setError(e.message)
      } finally {
        setLoading(false)
      }
    }
    fetchState()
  }, [sandboxId])

  const findFirstFile = (nodes: FileSystemNode[]): FileSystemNode | null => {
    for (const node of nodes) {
      if (node.type === "file") return node
      if (node.type === "directory" && node.children) {
        const found = findFirstFile(node.children)
        if (found) return found
      }
    }
    return null
  }

  if (!sandboxId) {
    return (
      <aside className="w-1/2 flex flex-col items-center justify-center bg-card border-l p-4 text-center">
        <Terminal className="w-12 h-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold text-foreground">No Active Sandbox</h3>
        <p className="text-muted-foreground text-sm">
          Ask the agent to build something (e.g., "create a login form") to start a new sandbox session.
        </p>
      </aside>
    )
  }

  if (loading) {
    return (
      <aside className="w-1/2 flex flex-col items-center justify-center bg-card border-l">
        <LoaderCircle className="w-8 h-8 animate-spin text-muted-foreground" />
        <p className="mt-4 text-muted-foreground">Initializing Sandbox...</p>
      </aside>
    )
  }

  if (error || !state) {
    return (
      <aside className="w-1/2 flex flex-col items-center justify-center bg-card border-l text-destructive">
        <p>Error loading sandbox: {error}</p>
      </aside>
    )
  }

  return (
    <aside className="w-1/2 flex flex-col bg-card border-l relative transition-all duration-300 ease-in-out">
      <Button
        variant="ghost"
        size="icon"
        onClick={togglePanel}
        className="absolute top-2 left-2 z-10 text-muted-foreground hover:bg-secondary hover:text-foreground h-8 w-8"
        title="Collapse panel"
      >
        <PanelRightClose className="w-5 h-5" />
      </Button>
      <Tabs defaultValue="preview" className="flex flex-col h-full">
        <TabsList className="grid w-full grid-cols-4 bg-card border-b rounded-none h-14">
          <TabsTrigger
            value="preview"
            className="data-[state=active]:bg-secondary data-[state=active]:text-foreground text-muted-foreground"
          >
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </TabsTrigger>
          <TabsTrigger
            value="code"
            className="data-[state=active]:bg-secondary data-[state=active]:text-foreground text-muted-foreground"
          >
            <Code className="w-4 h-4 mr-2" />
            Code
          </TabsTrigger>
          <TabsTrigger
            value="vm"
            className="data-[state=active]:bg-secondary data-[state=active]:text-foreground text-muted-foreground"
          >
            <Terminal className="w-4 h-4 mr-2" />
            Terminal
          </TabsTrigger>
          <TabsTrigger
            value="docker"
            className="data-[state=active]:bg-secondary data-[state=active]:text-foreground text-muted-foreground"
          >
            <Container className="w-4 h-4 mr-2" />
            Docker
          </TabsTrigger>
        </TabsList>

        <TabsContent value="preview" className="flex-1 mt-0">
          <SandboxPreview url={state.previewUrl} />
        </TabsContent>

        <TabsContent value="code" className="flex-1 flex mt-0 overflow-hidden">
          <div className="w-1/3 border-r overflow-y-auto">
            <FileTree nodes={state.fileSystem} onSelectFile={setSelectedFile} selectedFile={selectedFile} />
          </div>
          <div className="w-2/3 overflow-y-auto">
            <CodeViewer file={selectedFile} />
          </div>
        </TabsContent>

        <TabsContent value="vm" className="flex-1 mt-0">
          <VirtualMachine logs={state.terminalOutput} sandboxId={sandboxId} />
        </TabsContent>

        <TabsContent value="docker" className="flex-1 mt-0">
          <DockerManager sandboxId={sandboxId} />
        </TabsContent>
      </Tabs>
    </aside>
  )
}
