"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Eye, EyeOff, Copy, Save } from "lucide-react"

type ApiKeyInputProps = {
  label: string
  id: string
  initialValue?: string
  onSave?: (value: string) => void
}

export default function ApiKeyInput({ label, id, initialValue = "", onSave }: ApiKeyInputProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [value, setValue] = useState(initialValue || "********************************")
  const [isEditing, setIsEditing] = useState(!initialValue)

  const handleCopy = () => {
    navigator.clipboard.writeText(value)
  }

  const handleSaveClick = () => {
    if (onSave) {
      onSave(value)
    }
    setIsEditing(false)
  }

  return (
    <div className="p-4 bg-secondary rounded-lg border">
      {label && (
        <Label htmlFor={id} className="text-sm font-medium text-muted-foreground block mb-2">
          {label}
        </Label>
      )}
      <div className="flex items-center gap-2">
        <div className="relative flex-grow">
          <Input
            id={id}
            type={isVisible ? "text" : "password"}
            value={value}
            onChange={(e) => {
              setValue(e.target.value)
              if (!isEditing) setIsEditing(true)
            }}
            className="bg-background border font-mono"
          />
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-2 top-1/2 -translate-y-1/2 h-7 w-7 text-muted-foreground hover:bg-accent"
            onClick={() => setIsVisible(!isVisible)}
          >
            {isVisible ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </Button>
        </div>
        <Button variant="outline" size="icon" className="h-9 w-9 bg-transparent" onClick={handleCopy}>
          <Copy className="w-4 h-4" />
        </Button>
        {onSave && (
          <Button
            size="sm"
            className="bg-accent-primary text-primary-foreground hover:bg-accent-secondary"
            onClick={handleSaveClick}
            disabled={!isEditing}
          >
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
        )}
      </div>
    </div>
  )
}
