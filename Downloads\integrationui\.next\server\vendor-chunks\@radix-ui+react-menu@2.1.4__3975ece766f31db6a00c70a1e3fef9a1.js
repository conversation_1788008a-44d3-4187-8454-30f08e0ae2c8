"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-menu@2.1.4__3975ece766f31db6a00c70a1e3fef9a1";
exports.ids = ["vendor-chunks/@radix-ui+react-menu@2.1.4__3975ece766f31db6a00c70a1e3fef9a1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.4__3975ece766f31db6a00c70a1e3fef9a1/node_modules/@radix-ui/react-menu/dist/index.mjs":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-menu@2.1.4__3975ece766f31db6a00c70a1e3fef9a1/node_modules/@radix-ui/react-menu/dist/index.mjs ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Anchor: () => (/* binding */ Anchor2),\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   CheckboxItem: () => (/* binding */ CheckboxItem),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Item: () => (/* binding */ Item2),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Menu: () => (/* binding */ Menu),\n/* harmony export */   MenuAnchor: () => (/* binding */ MenuAnchor),\n/* harmony export */   MenuArrow: () => (/* binding */ MenuArrow),\n/* harmony export */   MenuCheckboxItem: () => (/* binding */ MenuCheckboxItem),\n/* harmony export */   MenuContent: () => (/* binding */ MenuContent),\n/* harmony export */   MenuGroup: () => (/* binding */ MenuGroup),\n/* harmony export */   MenuItem: () => (/* binding */ MenuItem),\n/* harmony export */   MenuItemIndicator: () => (/* binding */ MenuItemIndicator),\n/* harmony export */   MenuLabel: () => (/* binding */ MenuLabel),\n/* harmony export */   MenuPortal: () => (/* binding */ MenuPortal),\n/* harmony export */   MenuRadioGroup: () => (/* binding */ MenuRadioGroup),\n/* harmony export */   MenuRadioItem: () => (/* binding */ MenuRadioItem),\n/* harmony export */   MenuSeparator: () => (/* binding */ MenuSeparator),\n/* harmony export */   MenuSub: () => (/* binding */ MenuSub),\n/* harmony export */   MenuSubContent: () => (/* binding */ MenuSubContent),\n/* harmony export */   MenuSubTrigger: () => (/* binding */ MenuSubTrigger),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup),\n/* harmony export */   RadioItem: () => (/* binding */ RadioItem),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Sub: () => (/* binding */ Sub),\n/* harmony export */   SubContent: () => (/* binding */ SubContent),\n/* harmony export */   SubTrigger: () => (/* binding */ SubTrigger),\n/* harmony export */   createMenuScope: () => (/* binding */ createMenuScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.16_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_228a7715bc1e7f1ef0f57e192c817a7e/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_26d951f800ba9ac5e485256218eadcb6/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_3e448a17430bb5cec37a28922ce2643b/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_8cbe1cf120528a31550a5afab2fb8eaa/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_d004b7752848a40db036ffac422f16d1/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guard_5dd7072ff118a6ad34ead5098941067d/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope_7f3285b117a4f9d53c19da3d2e56eadd/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@18.3.24_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._7d11218edfe03e57283b3812191c12d5/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._8787d661fc533083853657d6c08b3f77/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._c6aa41ad6b83d429f511a46c1630fa05/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_76448c71dc0bd9c322c4fe3e5c001014/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-roving-focus */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focu_8cd37efac78fba0de50c58efa0a18934/node_modules/@radix-ui/react-roving-focus/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.1_@types+react@18.3.24_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_cb4d45ba895b919d63e6b537447750dd/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.6/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.24_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.16_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Anchor,Arrow,CheckboxItem,Content,Group,Item,ItemIndicator,Label,Menu,MenuAnchor,MenuArrow,MenuCheckboxItem,MenuContent,MenuGroup,MenuItem,MenuItemIndicator,MenuLabel,MenuPortal,MenuRadioGroup,MenuRadioItem,MenuSeparator,MenuSub,MenuSubContent,MenuSubTrigger,Portal,RadioGroup,RadioItem,Root,Separator,Sub,SubContent,SubTrigger,createMenuScope auto */ // packages/react/menu/src/Menu.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar SELECTION_KEYS = [\n    \"Enter\",\n    \" \"\n];\nvar FIRST_KEYS = [\n    \"ArrowDown\",\n    \"PageUp\",\n    \"Home\"\n];\nvar LAST_KEYS = [\n    \"ArrowUp\",\n    \"PageDown\",\n    \"End\"\n];\nvar FIRST_LAST_KEYS = [\n    ...FIRST_KEYS,\n    ...LAST_KEYS\n];\nvar SUB_OPEN_KEYS = {\n    ltr: [\n        ...SELECTION_KEYS,\n        \"ArrowRight\"\n    ],\n    rtl: [\n        ...SELECTION_KEYS,\n        \"ArrowLeft\"\n    ]\n};\nvar SUB_CLOSE_KEYS = {\n    ltr: [\n        \"ArrowLeft\"\n    ],\n    rtl: [\n        \"ArrowRight\"\n    ]\n};\nvar MENU_NAME = \"Menu\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(MENU_NAME);\nvar [createMenuContext, createMenuScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(MENU_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.createPopperScope,\n    _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.createRovingFocusGroupScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.createPopperScope)();\nvar useRovingFocusGroupScope = (0,_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.createRovingFocusGroupScope)();\nvar [MenuProvider, useMenuContext] = createMenuContext(MENU_NAME);\nvar [MenuRootProvider, useMenuRootContext] = createMenuContext(MENU_NAME);\nvar Menu = (props)=>{\n    const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const isUsingKeyboardRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleOpenChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onOpenChange);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__.useDirection)(dir);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = ()=>{\n            isUsingKeyboardRef.current = true;\n            document.addEventListener(\"pointerdown\", handlePointer, {\n                capture: true,\n                once: true\n            });\n            document.addEventListener(\"pointermove\", handlePointer, {\n                capture: true,\n                once: true\n            });\n        };\n        const handlePointer = ()=>isUsingKeyboardRef.current = false;\n        document.addEventListener(\"keydown\", handleKeyDown, {\n            capture: true\n        });\n        return ()=>{\n            document.removeEventListener(\"keydown\", handleKeyDown, {\n                capture: true\n            });\n            document.removeEventListener(\"pointerdown\", handlePointer, {\n                capture: true\n            });\n            document.removeEventListener(\"pointermove\", handlePointer, {\n                capture: true\n            });\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuProvider, {\n            scope: __scopeMenu,\n            open,\n            onOpenChange: handleOpenChange,\n            content,\n            onContentChange: setContent,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootProvider, {\n                scope: __scopeMenu,\n                onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>handleOpenChange(false), [\n                    handleOpenChange\n                ]),\n                isUsingKeyboardRef,\n                dir: direction,\n                modal,\n                children\n            })\n        })\n    });\n};\nMenu.displayName = MENU_NAME;\nvar ANCHOR_NAME = \"MenuAnchor\";\nvar MenuAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Anchor, {\n        ...popperScope,\n        ...anchorProps,\n        ref: forwardedRef\n    });\n});\nMenuAnchor.displayName = ANCHOR_NAME;\nvar PORTAL_NAME = \"MenuPortal\";\nvar [PortalProvider, usePortalContext] = createMenuContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar MenuPortal = (props)=>{\n    const { __scopeMenu, forceMount, children, container } = props;\n    const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeMenu,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nMenuPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"MenuContent\";\nvar [MenuContentProvider, useMenuContentContext] = createMenuContext(CONTENT_NAME);\nvar MenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeMenu,\n                children: rootContext.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootContentModal, {\n                    ...contentProps,\n                    ref: forwardedRef\n                }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootContentNonModal, {\n                    ...contentProps,\n                    ref: forwardedRef\n                })\n            })\n        })\n    });\n});\nvar MenuRootContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = ref.current;\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_11__.hideOthers)(content);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: context.open,\n        disableOutsideScroll: true,\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault(), {\n            checkForDefaultPrevented: false\n        }),\n        onDismiss: ()=>context.onOpenChange(false)\n    });\n});\nvar MenuRootContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        disableOutsideScroll: false,\n        onDismiss: ()=>context.onOpenChange(false)\n    });\n});\nvar MenuContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, loop = false, trapFocus, onOpenAutoFocus, onCloseAutoFocus, disableOutsidePointerEvents, onEntryFocus, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, disableOutsideScroll, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const pointerGraceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerGraceIntentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerDirRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"right\");\n    const lastPointerXRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const ScrollLockWrapper = disableOutsideScroll ? react_remove_scroll__WEBPACK_IMPORTED_MODULE_13__[\"default\"] : react__WEBPACK_IMPORTED_MODULE_0__.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll ? {\n        as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__.Slot,\n        allowPinchZoom: true\n    } : void 0;\n    const handleTypeaheadSearch = (key)=>{\n        const search = searchRef.current + key;\n        const items = getItems().filter((item)=>!item.disabled);\n        const currentItem = document.activeElement;\n        const currentMatch = items.find((item)=>item.ref.current === currentItem)?.textValue;\n        const values = items.map((item)=>item.textValue);\n        const nextMatch = getNextMatch(values, search, currentMatch);\n        const newItem = items.find((item)=>item.textValue === nextMatch)?.ref.current;\n        (function updateSearch(value) {\n            searchRef.current = value;\n            window.clearTimeout(timerRef.current);\n            if (value !== \"\") timerRef.current = window.setTimeout(()=>updateSearch(\"\"), 1e3);\n        })(search);\n        if (newItem) {\n            setTimeout(()=>newItem.focus());\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>window.clearTimeout(timerRef.current);\n    }, []);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__.useFocusGuards)();\n    const isPointerMovingToSubmenu = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n        return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentProvider, {\n        scope: __scopeMenu,\n        searchRef,\n        onItemEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n        }, [\n            isPointerMovingToSubmenu\n        ]),\n        onItemLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n            if (isPointerMovingToSubmenu(event)) return;\n            contentRef.current?.focus();\n            setCurrentItemId(null);\n        }, [\n            isPointerMovingToSubmenu\n        ]),\n        onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n        }, [\n            isPointerMovingToSubmenu\n        ]),\n        pointerGraceTimerRef,\n        onPointerGraceIntentChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((intent)=>{\n            pointerGraceIntentRef.current = intent;\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollLockWrapper, {\n            ...scrollLockWrapperProps,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_16__.FocusScope, {\n                asChild: true,\n                trapped: trapFocus,\n                onMountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onOpenAutoFocus, (event)=>{\n                    event.preventDefault();\n                    contentRef.current?.focus({\n                        preventScroll: true\n                    });\n                }),\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_17__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside,\n                    onInteractOutside,\n                    onDismiss,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.Root, {\n                        asChild: true,\n                        ...rovingFocusGroupScope,\n                        dir: rootContext.dir,\n                        orientation: \"vertical\",\n                        loop,\n                        currentTabStopId: currentItemId,\n                        onCurrentTabStopIdChange: setCurrentItemId,\n                        onEntryFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEntryFocus, (event)=>{\n                            if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                        }),\n                        preventScrollOnEntryFocus: true,\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Content, {\n                            role: \"menu\",\n                            \"aria-orientation\": \"vertical\",\n                            \"data-state\": getOpenState(context.open),\n                            \"data-radix-menu-content\": \"\",\n                            dir: rootContext.dir,\n                            ...popperScope,\n                            ...contentProps,\n                            ref: composedRefs,\n                            style: {\n                                outline: \"none\",\n                                ...contentProps.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                                const target = event.target;\n                                const isKeyDownInside = target.closest(\"[data-radix-menu-content]\") === event.currentTarget;\n                                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                                const isCharacterKey = event.key.length === 1;\n                                if (isKeyDownInside) {\n                                    if (event.key === \"Tab\") event.preventDefault();\n                                    if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                                }\n                                const content = contentRef.current;\n                                if (event.target !== content) return;\n                                if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                                event.preventDefault();\n                                const items = getItems().filter((item)=>!item.disabled);\n                                const candidateNodes = items.map((item)=>item.ref.current);\n                                if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                                focusFirst(candidateNodes);\n                            }),\n                            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onBlur, (event)=>{\n                                if (!event.currentTarget.contains(event.target)) {\n                                    window.clearTimeout(timerRef.current);\n                                    searchRef.current = \"\";\n                                }\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                                const target = event.target;\n                                const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n                                if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                                    const newDir = event.clientX > lastPointerXRef.current ? \"right\" : \"left\";\n                                    pointerDirRef.current = newDir;\n                                    lastPointerXRef.current = event.clientX;\n                                }\n                            }))\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nMenuContent.displayName = CONTENT_NAME;\nvar GROUP_NAME = \"MenuGroup\";\nvar MenuGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...groupProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        role: \"group\",\n        ...groupProps,\n        ref: forwardedRef\n    });\n});\nMenuGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"MenuLabel\";\nvar MenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...labelProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nMenuLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"MenuItem\";\nvar ITEM_SELECT = \"menu.itemSelect\";\nvar MenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleSelect = ()=>{\n        const menuItem = ref.current;\n        if (!disabled && menuItem) {\n            const itemSelectEvent = new CustomEvent(ITEM_SELECT, {\n                bubbles: true,\n                cancelable: true\n            });\n            menuItem.addEventListener(ITEM_SELECT, (event)=>onSelect?.(event), {\n                once: true\n            });\n            (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.dispatchDiscreteCustomEvent)(menuItem, itemSelectEvent);\n            if (itemSelectEvent.defaultPrevented) {\n                isPointerDownRef.current = false;\n            } else {\n                rootContext.onClose();\n            }\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItemImpl, {\n        ...itemProps,\n        ref: composedRefs,\n        disabled,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, handleSelect),\n        onPointerDown: (event)=>{\n            props.onPointerDown?.(event);\n            isPointerDownRef.current = true;\n        },\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n            if (!isPointerDownRef.current) event.currentTarget?.click();\n        }),\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n            const isTypingAhead = contentContext.searchRef.current !== \"\";\n            if (disabled || isTypingAhead && event.key === \" \") return;\n            if (SELECTION_KEYS.includes(event.key)) {\n                event.currentTarget.click();\n                event.preventDefault();\n            }\n        })\n    });\n});\nMenuItem.displayName = ITEM_NAME;\nvar MenuItemImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [textContent, setTextContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"\");\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const menuItem = ref.current;\n        if (menuItem) {\n            setTextContent((menuItem.textContent ?? \"\").trim());\n        }\n    }, [\n        itemProps.children\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeMenu,\n        disabled,\n        textValue: textValue ?? textContent,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.Item, {\n            asChild: true,\n            ...rovingFocusGroupScope,\n            focusable: !disabled,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n                role: \"menuitem\",\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                ...itemProps,\n                ref: composedRefs,\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                    if (disabled) {\n                        contentContext.onItemLeave(event);\n                    } else {\n                        contentContext.onItemEnter(event);\n                        if (!event.defaultPrevented) {\n                            const item = event.currentTarget;\n                            item.focus({\n                                preventScroll: true\n                            });\n                        }\n                    }\n                })),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse((event)=>contentContext.onItemLeave(event))),\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onBlur, ()=>setIsFocused(false))\n            })\n        })\n    });\n});\nvar CHECKBOX_ITEM_NAME = \"MenuCheckboxItem\";\nvar MenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ItemIndicatorProvider, {\n        scope: props.__scopeMenu,\n        checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItem, {\n            role: \"menuitemcheckbox\",\n            \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n            ...checkboxItemProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(checked),\n            onSelect: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(checkboxItemProps.onSelect, ()=>onCheckedChange?.(isIndeterminate(checked) ? true : !checked), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\nvar RADIO_GROUP_NAME = \"MenuRadioGroup\";\nvar [RadioGroupProvider, useRadioGroupContext] = createMenuContext(RADIO_GROUP_NAME, {\n    value: void 0,\n    onValueChange: ()=>{}\n});\nvar MenuRadioGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onValueChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RadioGroupProvider, {\n        scope: props.__scopeMenu,\n        value,\n        onValueChange: handleValueChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuGroup, {\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\nvar RADIO_ITEM_NAME = \"MenuRadioItem\";\nvar MenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ItemIndicatorProvider, {\n        scope: props.__scopeMenu,\n        checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItem, {\n            role: \"menuitemradio\",\n            \"aria-checked\": checked,\n            ...radioItemProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(checked),\n            onSelect: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(radioItemProps.onSelect, ()=>context.onValueChange?.(value), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\nvar ITEM_INDICATOR_NAME = \"MenuItemIndicator\";\nvar [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext(ITEM_INDICATOR_NAME, {\n    checked: false\n});\nvar MenuItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || isIndeterminate(indicatorContext.checked) || indicatorContext.checked === true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.span, {\n            ...itemIndicatorProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(indicatorContext.checked)\n        })\n    });\n});\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SEPARATOR_NAME = \"MenuSeparator\";\nvar MenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        role: \"separator\",\n        \"aria-orientation\": \"horizontal\",\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nMenuSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"MenuArrow\";\nvar MenuArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nMenuArrow.displayName = ARROW_NAME;\nvar SUB_NAME = \"MenuSub\";\nvar [MenuSubProvider, useMenuSubContext] = createMenuContext(SUB_NAME);\nvar MenuSub = (props)=>{\n    const { __scopeMenu, children, open = false, onOpenChange } = props;\n    const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const handleOpenChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onOpenChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (parentMenuContext.open === false) handleOpenChange(false);\n        return ()=>handleOpenChange(false);\n    }, [\n        parentMenuContext.open,\n        handleOpenChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuProvider, {\n            scope: __scopeMenu,\n            open,\n            onOpenChange: handleOpenChange,\n            content,\n            onContentChange: setContent,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuSubProvider, {\n                scope: __scopeMenu,\n                contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__.useId)(),\n                triggerId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__.useId)(),\n                trigger,\n                onTriggerChange: setTrigger,\n                children\n            })\n        })\n    });\n};\nMenuSub.displayName = SUB_NAME;\nvar SUB_TRIGGER_NAME = \"MenuSubTrigger\";\nvar MenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = {\n        __scopeMenu: props.__scopeMenu\n    };\n    const clearOpenTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = null;\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>clearOpenTimer, [\n        clearOpenTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const pointerGraceTimer = pointerGraceTimerRef.current;\n        return ()=>{\n            window.clearTimeout(pointerGraceTimer);\n            onPointerGraceIntentChange(null);\n        };\n    }, [\n        pointerGraceTimerRef,\n        onPointerGraceIntentChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuAnchor, {\n        asChild: true,\n        ...scope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItemImpl, {\n            id: subContext.triggerId,\n            \"aria-haspopup\": \"menu\",\n            \"aria-expanded\": context.open,\n            \"aria-controls\": subContext.contentId,\n            \"data-state\": getOpenState(context.open),\n            ...props,\n            ref: (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.composeRefs)(forwardedRef, subContext.onTriggerChange),\n            onClick: (event)=>{\n                props.onClick?.(event);\n                if (props.disabled || event.defaultPrevented) return;\n                event.currentTarget.focus();\n                if (!context.open) context.onOpenChange(true);\n            },\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                contentContext.onItemEnter(event);\n                if (event.defaultPrevented) return;\n                if (!props.disabled && !context.open && !openTimerRef.current) {\n                    contentContext.onPointerGraceIntentChange(null);\n                    openTimerRef.current = window.setTimeout(()=>{\n                        context.onOpenChange(true);\n                        clearOpenTimer();\n                    }, 100);\n                }\n            })),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse((event)=>{\n                clearOpenTimer();\n                const contentRect = context.content?.getBoundingClientRect();\n                if (contentRect) {\n                    const side = context.content?.dataset.side;\n                    const rightSide = side === \"right\";\n                    const bleed = rightSide ? -5 : 5;\n                    const contentNearEdge = contentRect[rightSide ? \"left\" : \"right\"];\n                    const contentFarEdge = contentRect[rightSide ? \"right\" : \"left\"];\n                    contentContext.onPointerGraceIntentChange({\n                        area: [\n                            // Apply a bleed on clientX to ensure that our exit point is\n                            // consistently within polygon bounds\n                            {\n                                x: event.clientX + bleed,\n                                y: event.clientY\n                            },\n                            {\n                                x: contentNearEdge,\n                                y: contentRect.top\n                            },\n                            {\n                                x: contentFarEdge,\n                                y: contentRect.top\n                            },\n                            {\n                                x: contentFarEdge,\n                                y: contentRect.bottom\n                            },\n                            {\n                                x: contentNearEdge,\n                                y: contentRect.bottom\n                            }\n                        ],\n                        side\n                    });\n                    window.clearTimeout(pointerGraceTimerRef.current);\n                    pointerGraceTimerRef.current = window.setTimeout(()=>contentContext.onPointerGraceIntentChange(null), 300);\n                } else {\n                    contentContext.onTriggerLeave(event);\n                    if (event.defaultPrevented) return;\n                    contentContext.onPointerGraceIntentChange(null);\n                }\n            })),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                const isTypingAhead = contentContext.searchRef.current !== \"\";\n                if (props.disabled || isTypingAhead && event.key === \" \") return;\n                if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n                    context.onOpenChange(true);\n                    context.content?.focus();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\nvar SUB_CONTENT_NAME = \"MenuSubContent\";\nvar MenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeMenu,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n                    id: subContext.contentId,\n                    \"aria-labelledby\": subContext.triggerId,\n                    ...subContentProps,\n                    ref: composedRefs,\n                    align: \"start\",\n                    side: rootContext.dir === \"rtl\" ? \"left\" : \"right\",\n                    disableOutsidePointerEvents: false,\n                    disableOutsideScroll: false,\n                    trapFocus: false,\n                    onOpenAutoFocus: (event)=>{\n                        if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                        event.preventDefault();\n                    },\n                    onCloseAutoFocus: (event)=>event.preventDefault(),\n                    onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>{\n                        if (event.target !== subContext.trigger) context.onOpenChange(false);\n                    }),\n                    onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onEscapeKeyDown, (event)=>{\n                        rootContext.onClose();\n                        event.preventDefault();\n                    }),\n                    onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                        const isKeyDownInside = event.currentTarget.contains(event.target);\n                        const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                        if (isKeyDownInside && isCloseKey) {\n                            context.onOpenChange(false);\n                            subContext.trigger?.focus();\n                            event.preventDefault();\n                        }\n                    })\n                })\n            })\n        })\n    });\n});\nMenuSubContent.displayName = SUB_CONTENT_NAME;\nfunction getOpenState(open) {\n    return open ? \"open\" : \"closed\";\n}\nfunction isIndeterminate(checked) {\n    return checked === \"indeterminate\";\n}\nfunction getCheckedState(checked) {\n    return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\nfunction focusFirst(candidates) {\n    const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n    for (const candidate of candidates){\n        if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n        candidate.focus();\n        if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n    }\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nfunction getNextMatch(values, search, currentMatch) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n    let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n    const excludeCurrentMatch = normalizedSearch.length === 1;\n    if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v)=>v !== currentMatch);\n    const nextMatch = wrappedValues.find((value)=>value.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextMatch !== currentMatch ? nextMatch : void 0;\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const xi = polygon[i].x;\n        const yi = polygon[i].y;\n        const xj = polygon[j].x;\n        const yj = polygon[j].y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction isPointerInGraceArea(event, area) {\n    if (!area) return false;\n    const cursorPos = {\n        x: event.clientX,\n        y: event.clientY\n    };\n    return isPointInPolygon(cursorPos, area);\n}\nfunction whenMouse(handler) {\n    return (event)=>event.pointerType === \"mouse\" ? handler(event) : void 0;\n}\nvar Root3 = Menu;\nvar Anchor2 = MenuAnchor;\nvar Portal = MenuPortal;\nvar Content2 = MenuContent;\nvar Group = MenuGroup;\nvar Label = MenuLabel;\nvar Item2 = MenuItem;\nvar CheckboxItem = MenuCheckboxItem;\nvar RadioGroup = MenuRadioGroup;\nvar RadioItem = MenuRadioItem;\nvar ItemIndicator = MenuItemIndicator;\nvar Separator = MenuSeparator;\nvar Arrow2 = MenuArrow;\nvar Sub = MenuSub;\nvar SubTrigger = MenuSubTrigger;\nvar SubContent = MenuSubContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.4__3975ece766f31db6a00c70a1e3fef9a1/node_modules/@radix-ui/react-menu/dist/index.mjs\n");

/***/ })

};
;