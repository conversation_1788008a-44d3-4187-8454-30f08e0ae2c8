"use client"

import { useState } from "react"
import { Folder, File, ChevronRight, ChevronDown } from "lucide-react"
import type { ReplitFile } from "@/lib/types"
import { cn } from "@/lib/utils"

type ReplitFileTreeProps = {
  files: ReplitFile[]
  onSelectFile: (file: ReplitFile) => void
  selectedFile: ReplitFile | null
}

const FileNode = ({
  file,
  onSelectFile,
  selectedFile,
}: {
  file: ReplitFile
  onSelectFile: (file: ReplitFile) => void
  selectedFile: ReplitFile | null
}) => {
  const [isOpen, setIsOpen] = useState(true)
  const isSelected = selectedFile?.id === file.id

  if (file.type === "directory") {
    return (
      <div className="pl-4">
        <div
          className="flex items-center cursor-pointer py-1 px-2 rounded hover:bg-secondary"
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? <ChevronDown className="w-4 h-4 mr-1" /> : <ChevronRight className="w-4 h-4 mr-1" />}
          <Folder className="w-4 h-4 mr-2 text-yellow-500" />
          <span>{file.name}</span>
        </div>
        {isOpen && file.children && (
          <div className="border-l border-border ml-2">
            {file.children.map((child) => (
              <FileNode key={child.id} file={child} onSelectFile={onSelectFile} selectedFile={selectedFile} />
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="pl-4">
      <div
        className={cn(
          "flex items-center cursor-pointer py-1 px-2 rounded hover:bg-secondary",
          isSelected && "bg-accent-primary/20",
        )}
        onClick={() => onSelectFile(file)}
      >
        <File className="w-4 h-4 mr-2 text-blue-500" />
        <span>{file.name}</span>
      </div>
    </div>
  )
}

export default function ReplitFileTree({ files, onSelectFile, selectedFile }: ReplitFileTreeProps) {
  return (
    <div className="p-2 text-sm">
      {files.map((file) => (
        <FileNode key={file.id} file={file} onSelectFile={onSelectFile} selectedFile={selectedFile} />
      ))}
    </div>
  )
}
