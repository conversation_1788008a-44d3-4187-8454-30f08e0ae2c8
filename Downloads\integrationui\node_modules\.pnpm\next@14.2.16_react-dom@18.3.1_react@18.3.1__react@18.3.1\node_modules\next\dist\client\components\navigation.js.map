{"version": 3, "sources": ["../../../src/client/components/navigation.ts"], "names": ["ReadonlyURLSearchParams", "RedirectType", "ServerInsertedHTMLContext", "notFound", "permanentRedirect", "redirect", "useParams", "usePathname", "useRouter", "useSearchParams", "useSelectedLayoutSegment", "useSelectedLayoutSegments", "useServerInsertedHTML", "searchParams", "useContext", "SearchParamsContext", "readonlySearchParams", "useMemo", "window", "bailoutToClientRendering", "require", "PathnameContext", "router", "AppRouterContext", "Error", "PathParamsContext", "getSelectedLayoutSegmentPath", "tree", "parallelRouteKey", "first", "segmentPath", "node", "parallelRoutes", "children", "Object", "values", "segment", "segmentValue", "getSegmentValue", "startsWith", "PAGE_SEGMENT_KEY", "push", "context", "LayoutRouterContext", "selectedLayoutSegments", "length", "selectedLayoutSegment", "DEFAULT_SEGMENT_KEY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;IA8QEA,uBAAuB;eAAvBA,8CAAuB;;IADvBC,YAAY;eAAZA,mCAAY;;IARZC,yBAAyB;eAAzBA,0DAAyB;;IAKzBC,QAAQ;eAARA,+BAAQ;;IAERC,iBAAiB;eAAjBA,wCAAiB;;IADjBC,QAAQ;eAARA,+BAAQ;;IATRC,SAAS;eAATA;;IAHAC,WAAW;eAAXA;;IAIAC,SAAS;eAATA;;IALAC,eAAe;eAAfA;;IAEAC,wBAAwB;eAAxBA;;IACAC,yBAAyB;eAAzBA;;IAGAC,qBAAqB;eAArBA,sDAAqB;;;uBApQa;+CAM7B;iDAKA;iCACyB;yBACsB;uCACd;iDA2EjC;AAzEP;;;;;;;;;;;;;;;;;;;CAmBC,GACD,SAASH;IACP,MAAMI,eAAeC,IAAAA,iBAAU,EAACC,oDAAmB;IAEnD,8DAA8D;IAC9D,0EAA0E;IAC1E,kBAAkB;IAClB,MAAMC,uBAAuBC,IAAAA,cAAO,EAAC;QACnC,IAAI,CAACJ,cAAc;YACjB,yEAAyE;YACzE,aAAa;YACb,OAAO;QACT;QAEA,OAAO,IAAIb,8CAAuB,CAACa;IACrC,GAAG;QAACA;KAAa;IAEjB,IAAI,OAAOK,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEC,wBAAwB,EAAE,GAChCC,QAAQ;QACV,mEAAmE;QACnED,yBAAyB;IAC3B;IAEA,OAAOH;AACT;AAEA;;;;;;;;;;;;;;;;CAgBC,GACD,SAAST;IACP,8EAA8E;IAC9E,0EAA0E;IAC1E,OAAOO,IAAAA,iBAAU,EAACO,gDAAe;AACnC;AAOA;;;;;;;;;;;;;;;;;CAiBC,GACD,SAASb;IACP,MAAMc,SAASR,IAAAA,iBAAU,EAACS,+CAAgB;IAC1C,IAAID,WAAW,MAAM;QACnB,MAAM,IAAIE,MAAM;IAClB;IAEA,OAAOF;AACT;AAMA;;;;;;;;;;;;;;;;CAgBC,GACD,SAAShB;IACP,OAAOQ,IAAAA,iBAAU,EAACW,kDAAiB;AACrC;AAEA,0EAA0E,GAC1E,SAASC,6BACPC,IAAuB,EACvBC,gBAAwB,EACxBC,KAAY,EACZC,WAA0B;IAD1BD,IAAAA,kBAAAA,QAAQ;IACRC,IAAAA,wBAAAA,cAAwB,EAAE;IAE1B,IAAIC;IACJ,IAAIF,OAAO;QACT,kEAAkE;QAClEE,OAAOJ,IAAI,CAAC,EAAE,CAACC,iBAAiB;IAClC,OAAO;QACL,oGAAoG;QACpG,MAAMI,iBAAiBL,IAAI,CAAC,EAAE;YACvBK;QAAPD,OAAOC,CAAAA,2BAAAA,eAAeC,QAAQ,YAAvBD,2BAA2BE,OAAOC,MAAM,CAACH,eAAe,CAAC,EAAE;IACpE;IAEA,IAAI,CAACD,MAAM,OAAOD;IAClB,MAAMM,UAAUL,IAAI,CAAC,EAAE;IAEvB,MAAMM,eAAeC,IAAAA,gCAAe,EAACF;IACrC,IAAI,CAACC,gBAAgBA,aAAaE,UAAU,CAACC,yBAAgB,GAAG;QAC9D,OAAOV;IACT;IAEAA,YAAYW,IAAI,CAACJ;IAEjB,OAAOX,6BACLK,MACAH,kBACA,OACAE;AAEJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAASnB,0BACPiB,gBAAqC;IAArCA,IAAAA,6BAAAA,mBAA2B;IAE3B,MAAMc,UAAU5B,IAAAA,iBAAU,EAAC6B,kDAAmB;IAC9C,wFAAwF;IACxF,IAAI,CAACD,SAAS,OAAO;IAErB,OAAOhB,6BAA6BgB,QAAQf,IAAI,EAAEC;AACpD;AAEA;;;;;;;;;;;;;;;;;CAiBC,GACD,SAASlB,yBACPkB,gBAAqC;IAArCA,IAAAA,6BAAAA,mBAA2B;IAE3B,MAAMgB,yBAAyBjC,0BAA0BiB;IAEzD,IAAI,CAACgB,0BAA0BA,uBAAuBC,MAAM,KAAK,GAAG;QAClE,OAAO;IACT;IAEA,MAAMC,wBACJlB,qBAAqB,aACjBgB,sBAAsB,CAAC,EAAE,GACzBA,sBAAsB,CAACA,uBAAuBC,MAAM,GAAG,EAAE;IAE/D,yGAAyG;IACzG,yEAAyE;IACzE,OAAOC,0BAA0BC,4BAAmB,GAChD,OACAD;AACN"}