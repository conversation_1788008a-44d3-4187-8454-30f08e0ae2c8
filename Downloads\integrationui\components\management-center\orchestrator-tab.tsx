"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "lucide-react"
import Agent<PERSON><PERSON> from "@/components/management-center/agent-card"
import type { AgentConfig } from "@/lib/agent-types"

const initialAgents: AgentConfig[] = Array.from({ length: 5 }, (_, i) => ({
  id: `agent_${i + 1}`,
  name: `Agent ${i + 1}`,
  apiKey: "",
  model: "gpt-4",
  prompt: "You are a helpful assistant.",
  role: "Researcher",
  tools: [],
}))

export default function OrchestratorTab() {
  const [agents, setAgents] = useState<AgentConfig[]>(initialAgents)

  const handleSaveAgent = (updatedAgent: AgentConfig) => {
    setAgents((prevAgents) => prevAgents.map((agent) => (agent.id === updatedAgent.id ? updatedAgent : agent)))
    console.log("Saved agent:", updatedAgent)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-foreground flex items-center">
          <Bot className="w-6 h-6 mr-3 text-muted-foreground" />
          Agent Configuration
        </h2>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {agents.map((agent) => (
          <AgentCard key={agent.id} agent={agent} onSave={handleSaveAgent} />
        ))}
      </div>
    </div>
  )
}
