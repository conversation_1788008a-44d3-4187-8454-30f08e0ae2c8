{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Toast/Toast.tsx"], "names": ["Toast", "onClick", "children", "className", "div", "data-nextjs-toast", "e", "preventDefault", "data-nextjs-toast-wrapper"], "mappings": ";;;;+BAQaA;;;eAAAA;;;;;iEARU;AAQhB,MAAMA,QAA8B,SAASA,MAAM,KAIzD;IAJyD,IAAA,EACxDC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACV,GAJyD;IAKxD,qBACE,qBAACC;QACCC,mBAAiB;QACjBJ,SAAS,CAACK;YACRA,EAAEC,cAAc;YAChB,OAAON,2BAAAA;QACT;QACAE,WAAWA;kBAEX,cAAA,qBAACC;YAAII,2BAAyB;sBAAEN;;;AAGtC"}