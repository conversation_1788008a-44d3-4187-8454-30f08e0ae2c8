{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/container/Errors.tsx"], "names": ["Errors", "styles", "isNextjsLink", "text", "startsWith", "getErrorSignature", "ev", "event", "type", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "reason", "name", "message", "stack", "_", "isAppDir", "errors", "initialDisplayState", "versionInfo", "activeError", "lookups", "setLookups", "useState", "readyErrors", "nextError", "useMemo", "ready", "next", "idx", "length", "e", "id", "push", "prev", "isLoading", "Boolean", "useEffect", "mounted", "getErrorByType", "then", "resolved", "m", "displayState", "setDisplayState", "activeIdx", "setActiveIndex", "previous", "useCallback", "v", "Math", "max", "min", "minimize", "hide", "fullscreen", "Overlay", "Toast", "className", "onClick", "div", "svg", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "circle", "cx", "cy", "r", "line", "x1", "y1", "x2", "y2", "span", "button", "data-nextjs-toast-errors-hide-button", "stopPropagation", "aria-label", "CloseIcon", "error", "isServerError", "includes", "getErrorSource", "errorDetails", "details", "warningTemplate", "serverContent", "clientContent", "warning", "hydrationErrorType", "getHydrationWarningType", "hydrationWarning", "replace", "Dialog", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "LeftRightDialogHeader", "close", "small", "data-nextjs-dialog-header-total-count", "VersionStalenessInfo", "h1", "p", "HotlinkedText", "matcher", "componentStackFrames", "PseudoHtmlDiff", "hydrationMismatchType", "firstContent", "second<PERSON><PERSON>nt", "DialogBody", "RuntimeError", "toString", "css"], "mappings": ";;;;;;;;;;;;;;;IAkEgBA,MAAM;eAANA;;IAwPHC,MAAM;eAANA;;;;;uBA1T6C;wBAMnD;wBAMA;uCAC+B;yBACd;uBACF;gCACS;8BAEH;2BACF;8BACG;sCACQ;6BAEN;+BACD;0CACC;oCAIxB;;;;;;;;;;AAiBP,SAASC,aAAaC,IAAY;IAChC,OAAOA,KAAKC,UAAU,CAAC;AACzB;AAEA,SAASC,kBAAkBC,EAAuB;IAChD,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,OAAQC,MAAMC,IAAI;QAChB,KAAKC,8BAAsB;QAC3B,KAAKC,kCAA0B;YAAE;gBAC/B,OAAO,AAAGH,MAAMI,MAAM,CAACC,IAAI,GAAC,OAAIL,MAAMI,MAAM,CAACE,OAAO,GAAC,OAAIN,MAAMI,MAAM,CAACG,KAAK;YAC7E;QACA;YAAS,CACT;IACF;IAEA,6DAA6D;IAC7D,MAAMC,IAAWR;IACjB,OAAO;AACT;AAEO,SAASP,OAAO,KAKT;IALS,IAAA,EACrBgB,QAAQ,EACRC,MAAM,EACNC,mBAAmB,EACnBC,WAAW,EACC,GALS;QA2NNC;IArNf,MAAM,CAACC,SAASC,WAAW,GAAGC,IAAAA,eAAQ,EACpC,CAAC;IAGH,MAAM,CAACC,aAAaC,UAAU,GAAGC,IAAAA,cAAO,EAEtC;QACA,IAAIC,QAA2B,EAAE;QACjC,IAAIC,OAAmC;QAEvC,6DAA6D;QAC7D,IAAK,IAAIC,MAAM,GAAGA,MAAMZ,OAAOa,MAAM,EAAE,EAAED,IAAK;YAC5C,MAAME,IAAId,MAAM,CAACY,IAAI;YACrB,MAAM,EAAEG,EAAE,EAAE,GAAGD;YACf,IAAIC,MAAMX,SAAS;gBACjBM,MAAMM,IAAI,CAACZ,OAAO,CAACW,GAAG;gBACtB;YACF;YAEA,6BAA6B;YAC7B,IAAIH,MAAM,GAAG;gBACX,MAAMK,OAAOjB,MAAM,CAACY,MAAM,EAAE;gBAC5B,IAAIxB,kBAAkB6B,UAAU7B,kBAAkB0B,IAAI;oBACpD;gBACF;YACF;YAEAH,OAAOG;YACP;QACF;QAEA,OAAO;YAACJ;YAAOC;SAAK;IACtB,GAAG;QAACX;QAAQI;KAAQ;IAEpB,MAAMc,YAAYT,IAAAA,cAAO,EAAU;QACjC,OAAOF,YAAYM,MAAM,GAAG,KAAKM,QAAQnB,OAAOa,MAAM;IACxD,GAAG;QAACb,OAAOa,MAAM;QAAEN,YAAYM,MAAM;KAAC;IAEtCO,IAAAA,gBAAS,EAAC;QACR,IAAIZ,aAAa,MAAM;YACrB;QACF;QACA,IAAIa,UAAU;QAEdC,IAAAA,8BAAc,EAACd,WAAWT,UAAUwB,IAAI,CACtC,CAACC;YACC,sEAAsE;YACtE,uEAAuE;YACvE,kBAAkB;YAClB,IAAIH,SAAS;gBACXhB,WAAW,CAACoB,IAAO,CAAA;wBAAE,GAAGA,CAAC;wBAAE,CAACD,SAAST,EAAE,CAAC,EAAES;oBAAS,CAAA;YACrD;QACF,GACA;QACE,yCAAyC;QAC3C;QAGF,OAAO;YACLH,UAAU;QACZ;IACF,GAAG;QAACb;QAAWT;KAAS;IAExB,MAAM,CAAC2B,cAAcC,gBAAgB,GACnCrB,IAAAA,eAAQ,EAAeL;IACzB,MAAM,CAAC2B,WAAWC,eAAe,GAAGvB,IAAAA,eAAQ,EAAS;IACrD,MAAMwB,WAAWC,IAAAA,kBAAW,EAC1B,IAAMF,eAAe,CAACG,IAAMC,KAAKC,GAAG,CAAC,GAAGF,IAAI,KAC5C,EAAE;IAEJ,MAAMrB,OAAOoB,IAAAA,kBAAW,EACtB,IACEF,eAAe,CAACG,IACdC,KAAKC,GAAG,CAAC,GAAGD,KAAKE,GAAG,CAAC5B,YAAYM,MAAM,GAAG,GAAGmB,IAAI,MAErD;QAACzB,YAAYM,MAAM;KAAC;IAGtB,MAAMV,cAAcM,IAAAA,cAAO,EACzB;YAAMF;eAAAA,CAAAA,yBAAAA,WAAW,CAACqB,UAAU,YAAtBrB,yBAA0B;IAAG,GACnC;QAACqB;QAAWrB;KAAY;IAG1B,kEAAkE;IAClE,gDAAgD;IAChDa,IAAAA,gBAAS,EAAC;QACR,IAAIpB,OAAOa,MAAM,GAAG,GAAG;YACrBR,WAAW,CAAC;YACZsB,gBAAgB;YAChBE,eAAe;QACjB;IACF,GAAG;QAAC7B,OAAOa,MAAM;KAAC;IAElB,MAAMuB,WAAWL,IAAAA,kBAAW,EAAC,IAAMJ,gBAAgB,cAAc,EAAE;IACnE,MAAMU,OAAON,IAAAA,kBAAW,EAAC,IAAMJ,gBAAgB,WAAW,EAAE;IAC5D,MAAMW,aAAaP,IAAAA,kBAAW,EAAC,IAAMJ,gBAAgB,eAAe,EAAE;IAEtE,2EAA2E;IAC3E,6CAA6C;IAC7C,IAAI3B,OAAOa,MAAM,GAAG,KAAKV,eAAe,MAAM;QAC5C,OAAO;IACT;IAEA,IAAIe,WAAW;QACb,6BAA6B;QAC7B,qBAAO,qBAACqB,gBAAO;IACjB;IAEA,IAAIb,iBAAiB,UAAU;QAC7B,OAAO;IACT;IAEA,IAAIA,iBAAiB,aAAa;QAChC,qBACE,qBAACc,YAAK;YAACC,WAAU;YAA6BC,SAASJ;sBACrD,cAAA,sBAACK;gBAAIF,WAAU;;kCACb,sBAACG;wBACCC,OAAM;wBACNC,OAAM;wBACNC,QAAO;wBACPC,SAAQ;wBACRC,MAAK;wBACLC,QAAO;wBACPC,aAAY;wBACZC,eAAc;wBACdC,gBAAe;;0CAEf,qBAACC;gCAAOC,IAAG;gCAAKC,IAAG;gCAAKC,GAAE;;0CAC1B,qBAACC;gCAAKC,IAAG;gCAAKC,IAAG;gCAAIC,IAAG;gCAAKC,IAAG;;0CAChC,qBAACJ;gCAAKC,IAAG;gCAAKC,IAAG;gCAAKC,IAAG;gCAAQC,IAAG;;;;kCAEtC,sBAACC;;4BACExD,YAAYM,MAAM;4BAAC;4BAAON,YAAYM,MAAM,GAAG,IAAI,MAAM;;;kCAE5D,qBAACmD;wBACCC,sCAAoC;wBACpCxB,WAAU;wBACVlD,MAAK;wBACLmD,SAAS,CAAC5B;4BACRA,EAAEoD,eAAe;4BACjB7B;wBACF;wBACA8B,cAAW;kCAEX,cAAA,qBAACC,oBAAS;;;;;IAKpB;IAEA,MAAMC,QAAQlE,YAAYkE,KAAK;IAC/B,MAAMC,gBAAgB;QAAC;QAAU;KAAc,CAACC,QAAQ,CACtDC,IAAAA,2BAAc,EAACH,UAAU;IAG3B,MAAMI,eAAoC,AAACJ,MAAcK,OAAO,IAAI,CAAC;IACrE,MAAM,CAACC,iBAAiBC,eAAeC,cAAc,GACnDJ,aAAaK,OAAO,IAAI;QAAC;QAAM;QAAI;KAAG;IAExC,MAAMC,qBAAqBC,IAAAA,2CAAuB,EAACL;IACnD,MAAMM,mBAAmBN,kBACrBA,gBACGO,OAAO,CAAC,MAAMN,eACdM,OAAO,CAAC,MAAML,eACdK,OAAO,CAAC,MAAM,IAAI,0BAA0B;KAC5CA,OAAO,CAAC,OAAO,IAAI,8CAA8C;KACjEA,OAAO,CAAC,cAAc,MACzB;IAEJ,qBACE,qBAAC3C,gBAAO;kBACN,cAAA,qBAAC4C,cAAM;YACL5F,MAAK;YACL6F,mBAAgB;YAChBC,oBAAiB;YACjBC,SAAShB,gBAAgBiB,YAAYnD;sBAErC,cAAA,sBAACoD,qBAAa;;kCACZ,sBAACC,oBAAY;wBAAChD,WAAU;;0CACtB,sBAACiD,4CAAqB;gCACpB5D,UAAUF,YAAY,IAAIE,WAAW;gCACrCnB,MAAMiB,YAAYrB,YAAYM,MAAM,GAAG,IAAIF,OAAO;gCAClDgF,OAAOrB,gBAAgBiB,YAAYnD;;kDAEnC,sBAACwD;;0DACC,qBAAC7B;0DAAMnC,YAAY;;4CAAS;4CAAI;0DAChC,qBAACmC;gDAAK8B,uCAAqC;0DACxCtF,YAAYM,MAAM;;4CAEpB;4CACAN,YAAYM,MAAM,GAAG,IAAI,KAAK;;;oCAEhCX,4BAAc,qBAAC4F,0CAAoB;wCAAE,GAAG5F,WAAW;yCAAO;;;0CAE7D,qBAAC6F;gCAAGhF,IAAG;0CACJuD,gBAAgB,iBAAiB;;0CAEpC,sBAAC0B;gCACCjF,IAAG;gCACH0B,WAAU;;oCAET4B,MAAM1E,IAAI;oCAAC;oCAAE;kDACd,qBAACsG,4BAAa;wCAAC/G,MAAMmF,MAAMzE,OAAO;wCAAEsG,SAASjH;;;;4BAE9CgG,kCACC;;kDACE,qBAACe;wCACCjF,IAAG;wCACH0B,WAAU;kDAETwC;;oCAEF9E,EAAAA,oCAAAA,YAAYgG,oBAAoB,qBAAhChG,kCAAkCU,MAAM,kBACvC,qBAACuF,wCAAc;wCACb3D,WAAU;wCACV4D,uBAAuBtB;wCACvBoB,sBAAsBhG,YAAYgG,oBAAoB;wCACtDG,cAAc1B;wCACd2B,eAAe1B;yCAEf;;;4BAGPP,8BACC,qBAAC3B;0CACC,cAAA,qBAACiD;8CAAM;;iCAKPL;;;kCAEN,qBAACiB,kBAAU;wBAAC/D,WAAU;kCACpB,cAAA,qBAACgE,0BAAY;4BAAiCpC,OAAOlE;2BAAlCA,YAAYY,EAAE,CAAC2F,QAAQ;;;;;;AAMtD;AAEO,MAAM1H,aAAS2H,kBAAG"}