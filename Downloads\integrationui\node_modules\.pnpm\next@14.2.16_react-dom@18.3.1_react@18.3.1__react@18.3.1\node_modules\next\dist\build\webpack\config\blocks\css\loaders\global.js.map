{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/global.ts"], "names": ["getGlobalCssLoader", "ctx", "postcss", "preProcessors", "loaders", "isClient", "push", "getClientStyleLoader", "hasAppDir", "isAppDir", "isDevelopment", "assetPrefix", "experimental", "useLightningcss", "loader", "require", "resolve", "options", "importLoaders", "length", "url", "resourcePath", "cssFileResolve", "urlImports", "import", "_", "targets", "supportedBrowsers", "modules", "slice", "reverse"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;wBAHqB;6BACN;AAExB,SAASA,mBACdC,GAAyB,EACzBC,OAAY,EACZC,gBAAmD,EAAE;IAErD,MAAMC,UAAoC,EAAE;IAE5C,IAAIH,IAAII,QAAQ,EAAE;QAChB,4DAA4D;QAC5D,SAAS;QACTD,QAAQE,IAAI,CACVC,IAAAA,4BAAoB,EAAC;YACnBC,WAAWP,IAAIO,SAAS;YACxBC,UAAUR,IAAIQ,QAAQ;YACtBC,eAAeT,IAAIS,aAAa;YAChCC,aAAaV,IAAIU,WAAW;QAC9B;IAEJ;IAEA,IAAIV,IAAIW,YAAY,CAACC,eAAe,EAAE;QACpCT,QAAQE,IAAI,CAAC;YACXQ,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPC,eAAe,IAAIf,cAAcgB,MAAM;gBACvCC,KAAK,CAACA,KAAaC,eACjBC,IAAAA,2BAAc,EAACF,KAAKC,cAAcpB,IAAIW,YAAY,CAACW,UAAU;gBAC/DC,QAAQ,CAACJ,KAAaK,GAAQJ,eAC5BC,IAAAA,2BAAc,EAACF,KAAKC,cAAcpB,IAAIW,YAAY,CAACW,UAAU;gBAC/DG,SAASzB,IAAI0B,iBAAiB;YAChC;QACF;IACF,OAAO;QACL,sCAAsC;QACtCvB,QAAQE,IAAI,CAAC;YACXQ,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPf;gBACAgB,eAAe,IAAIf,cAAcgB,MAAM;gBACvC,4CAA4C;gBAC5CS,SAAS;gBACTR,KAAK,CAACA,KAAaC,eACjBC,IAAAA,2BAAc,EAACF,KAAKC,cAAcpB,IAAIW,YAAY,CAACW,UAAU;gBAC/DC,QAAQ,CAACJ,KAAaK,GAAQJ,eAC5BC,IAAAA,2BAAc,EAACF,KAAKC,cAAcpB,IAAIW,YAAY,CAACW,UAAU;YACjE;QACF;QAEA,cAAc;QACdnB,QAAQE,IAAI,CAAC;YACXQ,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPf;YACF;QACF;IACF;IAEAE,QAAQE,IAAI,CACV,sEAAsE;IACtE,0BAA0B;OACvBH,cAAc0B,KAAK,GAAGC,OAAO;IAGlC,OAAO1B;AACT"}