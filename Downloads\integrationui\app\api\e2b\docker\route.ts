import { NextResponse } from "next/server"
import { Sandbox } from "e2b"

export async function POST(request: Request) {
  const { sandboxId, command, containerName, image, options } = await request.json()

  if (!sandboxId) {
    return NextResponse.json({ error: "Sandbox ID is required" }, { status: 400 })
  }

  try {
    const sandbox = await Sandbox.connect(sandboxId, {
      apiKey: process.env.E2B_API_KEY,
    })

    let result

    switch (command) {
      case "run":
        if (!image) {
          return NextResponse.json({ error: "Image is required for run command" }, { status: 400 })
        }
        const runOptions = options || {}
        const portMapping = runOptions.ports ? `-p ${runOptions.ports}` : ""
        const volumeMapping = runOptions.volumes ? `-v ${runOptions.volumes}` : ""
        const envVars = runOptions.env ? runOptions.env.map((env: string) => `-e ${env}`).join(" ") : ""

        result = await sandbox.commands.run(
          `docker run -d ${portMapping} ${volumeMapping} ${envVars} --name ${containerName || "container-" + Date.now()} ${image}`,
        )
        break

      case "start":
        if (!containerName) {
          return NextResponse.json({ error: "Container name is required for start command" }, { status: 400 })
        }
        result = await sandbox.commands.run(`docker start ${containerName}`)
        break

      case "restart":
        if (!containerName) {
          return NextResponse.json({ error: "Container name is required for restart command" }, { status: 400 })
        }
        result = await sandbox.commands.run(`docker restart ${containerName}`)
        break

      case "pause":
        if (!containerName) {
          return NextResponse.json({ error: "Container name is required for pause command" }, { status: 400 })
        }
        result = await sandbox.commands.run(`docker pause ${containerName}`)
        break

      case "unpause":
        if (!containerName) {
          return NextResponse.json({ error: "Container name is required for unpause command" }, { status: 400 })
        }
        result = await sandbox.commands.run(`docker unpause ${containerName}`)
        break

      case "ps":
        result = await sandbox.commands.run(
          "docker ps -a --format 'table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}\t{{.CreatedAt}}'",
        )
        break

      case "inspect":
        if (!containerName) {
          return NextResponse.json({ error: "Container name is required for inspect command" }, { status: 400 })
        }
        result = await sandbox.commands.run(`docker inspect ${containerName}`)
        break

      case "stats":
        if (!containerName) {
          return NextResponse.json({ error: "Container name is required for stats command" }, { status: 400 })
        }
        result = await sandbox.commands.run(
          `docker stats ${containerName} --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"`,
        )
        break

      case "logs":
        if (!containerName) {
          return NextResponse.json({ error: "Container name is required for logs command" }, { status: 400 })
        }
        result = await sandbox.commands.run(`docker logs ${containerName} --tail 100`)
        break

      case "stop":
        if (!containerName) {
          return NextResponse.json({ error: "Container name is required for stop command" }, { status: 400 })
        }
        result = await sandbox.commands.run(`docker stop ${containerName}`)
        break

      case "remove":
        if (!containerName) {
          return NextResponse.json({ error: "Container name is required for remove command" }, { status: 400 })
        }
        result = await sandbox.commands.run(`docker rm ${containerName}`)
        break

      case "images":
        result = await sandbox.commands.run(
          "docker images --format 'table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.Size}}\t{{.CreatedAt}}'",
        )
        break

      case "pull":
        if (!image) {
          return NextResponse.json({ error: "Image is required for pull command" }, { status: 400 })
        }
        result = await sandbox.commands.run(`docker pull ${image}`)
        break

      case "rmi":
        if (!image) {
          return NextResponse.json({ error: "Image is required for rmi command" }, { status: 400 })
        }
        result = await sandbox.commands.run(`docker rmi ${image}`)
        break

      case "image-inspect":
        if (!image) {
          return NextResponse.json({ error: "Image is required for image-inspect command" }, { status: 400 })
        }
        result = await sandbox.commands.run(`docker image inspect ${image}`)
        break

      case "search":
        if (!image) {
          return NextResponse.json({ error: "Search term is required for search command" }, { status: 400 })
        }
        result = await sandbox.commands.run(`docker search ${image} --limit 10`)
        break

      case "prune-images":
        result = await sandbox.commands.run("docker image prune -f")
        break

      case "system-df":
        result = await sandbox.commands.run("docker system df")
        break

      default:
        return NextResponse.json({ error: "Invalid command" }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      output: result.stdout,
      error: result.stderr,
      exitCode: result.exitCode,
    })
  } catch (error) {
    console.error("Docker command failed:", error)
    return NextResponse.json({ error: "Docker command failed" }, { status: 500 })
  }
}
