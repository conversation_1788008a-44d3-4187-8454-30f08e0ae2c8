{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.tsx"], "names": ["CodeFrame", "stackFrame", "codeFrame", "formattedFrame", "React", "useMemo", "lines", "split", "miniLeadingSpacesLength", "map", "line", "exec", "stripAnsi", "filter", "Boolean", "v", "pop", "reduce", "c", "n", "isNaN", "length", "Math", "min", "NaN", "a", "indexOf", "substring", "replace", "join", "decoded", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "use_classes", "remove_empty", "open", "useOpenInEditor", "file", "lineNumber", "column", "div", "data-nextjs-codeframe", "p", "role", "onClick", "tabIndex", "title", "span", "getFrameSource", "HotlinkedText", "text", "methodName", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "x1", "y1", "x2", "y2", "pre", "entry", "index", "style", "color", "fg", "undefined", "decoration", "fontWeight", "fontStyle", "content"], "mappings": ";;;;+BAUaA;;;eAAAA;;;;;;gEAVK;iEACK;oEAED;4BACS;iCACC;+BACF;AAIvB,MAAMA,YAAsC,SAASA,UAAU,KAGrE;IAHqE,IAAA,EACpEC,UAAU,EACVC,SAAS,EACV,GAHqE;IAIpE,8CAA8C;IAC9C,MAAMC,iBAAiBC,OAAMC,OAAO,CAAS;QAC3C,MAAMC,QAAQJ,UAAUK,KAAK,CAAC;QAE9B,wEAAwE;QACxE,MAAMC,0BAA0BF,MAC7BG,GAAG,CAAC,CAACC,OACJ,oBAAoBC,IAAI,CAACC,IAAAA,kBAAS,EAACF,WAAW,OAC1C,OACA,oBAAoBC,IAAI,CAACC,IAAAA,kBAAS,EAACF,QAExCG,MAAM,CAACC,SACPL,GAAG,CAAC,CAACM,IAAMA,EAAGC,GAAG,IACjBC,MAAM,CAAC,CAACC,GAAGC,IAAOC,MAAMF,KAAKC,EAAEE,MAAM,GAAGC,KAAKC,GAAG,CAACL,GAAGC,EAAEE,MAAM,GAAIG;QAEnE,2EAA2E;QAC3E,8FAA8F;QAC9F,IAAIhB,0BAA0B,GAAG;YAC/B,OAAOF,MACJG,GAAG,CAAC,CAACC,MAAMe,IACV,CAAEA,CAAAA,IAAIf,KAAKgB,OAAO,CAAC,IAAG,IAClBhB,KAAKiB,SAAS,CAAC,GAAGF,KAClBf,KAAKiB,SAAS,CAACF,GAAGG,OAAO,CAAC,AAAC,UAAOpB,0BAAwB,KAAI,MAC9DE,MAELmB,IAAI,CAAC;QACV;QACA,OAAOvB,MAAMuB,IAAI,CAAC;IACpB,GAAG;QAAC3B;KAAU;IAEd,MAAM4B,UAAU1B,OAAMC,OAAO,CAAC;QAC5B,OAAO0B,cAAK,CAACC,UAAU,CAAC7B,gBAAgB;YACtC8B,MAAM;YACNC,aAAa;YACbC,cAAc;QAChB;IACF,GAAG;QAAChC;KAAe;IAEnB,MAAMiC,OAAOC,IAAAA,gCAAe,EAAC;QAC3BC,MAAMrC,WAAWqC,IAAI;QACrBC,YAAYtC,WAAWsC,UAAU;QACjCC,QAAQvC,WAAWuC,MAAM;IAC3B;IAEA,gCAAgC;IAChC,qBACE,sBAACC;QAAIC,uBAAqB;;0BACxB,qBAACD;0BACC,cAAA,sBAACE;oBACCC,MAAK;oBACLC,SAAST;oBACTU,UAAU;oBACVC,OAAM;;sCAEN,sBAACC;;gCACEC,IAAAA,0BAAc,EAAChD;gCAAY;gCAAG;8CAC/B,qBAACiD,4BAAa;oCAACC,MAAMlD,WAAWmD,UAAU;;;;sCAE5C,sBAACC;4BACCC,OAAM;4BACNC,SAAQ;4BACRC,MAAK;4BACLC,QAAO;4BACPC,aAAY;4BACZC,eAAc;4BACdC,gBAAe;;8CAEf,qBAACC;oCAAKC,GAAE;;8CACR,qBAACC;oCAASC,QAAO;;8CACjB,qBAACtD;oCAAKuD,IAAG;oCAAKC,IAAG;oCAAKC,IAAG;oCAAKC,IAAG;;;;;;;0BAIvC,qBAACC;0BACEvC,QAAQrB,GAAG,CAAC,CAAC6D,OAAOC,sBACnB,qBAACvB;wBAECwB,OAAO;4BACLC,OAAOH,MAAMI,EAAE,GAAG,AAAC,iBAAcJ,MAAMI,EAAE,GAAC,MAAKC;4BAC/C,GAAIL,MAAMM,UAAU,KAAK,SACrB;gCAAEC,YAAY;4BAAI,IAClBP,MAAMM,UAAU,KAAK,WACrB;gCAAEE,WAAW;4BAAS,IACtBH,SAAS;wBACf;kCAECL,MAAMS,OAAO;uBAVT,AAAC,WAAQR;;;;AAgB1B"}