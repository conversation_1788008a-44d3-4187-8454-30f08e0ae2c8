import { NextResponse } from "next/server"
import { Sandbox } from "e2b"

export async function POST(request: Request) {
  const { prompt } = await request.json()

  try {
    // Create a new E2B sandbox with Docker support
    const sandbox = await Sandbox.create({
      template: "e2b-with-docker",
      apiKey: process.env.E2B_API_KEY,
    })

    console.log(`Created E2B sandbox with Docker support: ${sandbox.id}`)
    console.log(`Prompt: "${prompt}"`)

    // Initialize Docker environment
    await sandbox.commands.run("docker --version")

    return NextResponse.json({
      sandboxId: sandbox.id,
      status: "running",
    })
  } catch (error) {
    console.error("Failed to create E2B sandbox:", error)
    return NextResponse.json({ error: "Failed to create sandbox" }, { status: 500 })
  }
}
