import { NextResponse } from "next/server"
import type { ReplitState } from "@/lib/types"

// Real Replit API integration to fetch Repl state
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const replitId = searchParams.get("id")

  if (!replitId) {
    return NextResponse.json({ error: "Replit ID is required" }, { status: 400 })
  }

  try {
    // Fetch Repl information from Replit API
    const response = await fetch("https://replit.com/graphql", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.REPLIT_API_TOKEN}`,
        "X-Requested-With": "XMLHttpRequest",
      },
      body: JSON.stringify({
        query: `
          query GetRepl($id: String!) {
            repl(id: $id) {
              id
              title
              slug
              language
              url
              files {
                path
                content
              }
              isRunning
            }
          }
        `,
        variables: {
          id: replitId,
        },
      }),
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch Repl: ${response.statusText}`)
    }

    const data = await response.json()
    const repl = data.data.repl

    if (!repl) {
      throw new Error("Repl not found")
    }

    // Convert Replit files to our format
    const files = repl.files.map((file: any, index: number) => ({
      id: index.toString(),
      name: file.path.split("/").pop() || file.path,
      type: file.path.includes(".") ? "file" : "directory",
      content: file.content,
      language: getLanguageFromPath(file.path),
    }))

    // Build the state object
    const state: ReplitState = {
      replitId: repl.id,
      status: repl.isRunning ? "running" : "stopped",
      language: repl.language,
      title: repl.title,
      previewUrl: `${repl.url}`, // Direct link to the running Repl
      terminalOutput: [
        "Repl is running...",
        `Language: ${repl.language}`,
        `URL: ${repl.url}`,
        "Ready for interaction!",
      ],
      files,
    }

    return NextResponse.json(state)
  } catch (error) {
    console.error("Error fetching Repl state:", error)
    return NextResponse.json({ error: "Failed to fetch Repl state" }, { status: 500 })
  }
}

function getLanguageFromPath(path: string): string {
  const extension = path.split(".").pop()?.toLowerCase()

  const languageMap: { [key: string]: string } = {
    py: "python",
    js: "javascript",
    ts: "typescript",
    html: "html",
    css: "css",
    json: "json",
    md: "markdown",
    txt: "text",
    csv: "csv",
  }

  return languageMap[extension || ""] || "text"
}
