{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/use-websocket.ts"], "names": ["useSendMessage", "useTurbopack", "useWebsocket", "useWebsocketPing", "assetPrefix", "webSocketRef", "useRef", "useEffect", "current", "url", "getSocketUrl", "window", "WebSocket", "sendMessage", "useCallback", "data", "socket", "readyState", "OPEN", "send", "onUpdateError", "turbopackState", "init", "queue", "callback", "undefined", "processTurbopackMessage", "msg", "push", "initCurrent", "then", "connect", "addMessageListener", "cb", "websocketRef", "tree", "useContext", "GlobalLayoutRouterContext", "interval", "setInterval", "JSON", "stringify", "event", "appDirRoute", "clearInterval"], "mappings": ";;;;;;;;;;;;;;;;;IAqBgBA,cAAc;eAAdA;;IAcAC,YAAY;eAAZA;;IA9BAC,YAAY;eAAZA;;IAsFAC,gBAAgB;eAAhBA;;;uBA3F2C;+CACjB;8BACb;AAGtB,SAASD,aAAaE,WAAmB;IAC9C,MAAMC,eAAeC,IAAAA,aAAM;IAE3BC,IAAAA,gBAAS,EAAC;QACR,IAAIF,aAAaG,OAAO,EAAE;YACxB;QACF;QAEA,MAAMC,MAAMC,IAAAA,0BAAY,EAACN;QAEzBC,aAAaG,OAAO,GAAG,IAAIG,OAAOC,SAAS,CAAC,AAAC,KAAEH,MAAI;IACrD,GAAG;QAACL;KAAY;IAEhB,OAAOC;AACT;AAEO,SAASL,eAAeK,YAA6C;IAC1E,MAAMQ,cAAcC,IAAAA,kBAAW,EAC7B,CAACC;QACC,MAAMC,SAASX,aAAaG,OAAO;QACnC,IAAI,CAACQ,UAAUA,OAAOC,UAAU,KAAKD,OAAOE,IAAI,EAAE;YAChD;QACF;QACA,OAAOF,OAAOG,IAAI,CAACJ;IACrB,GACA;QAACV;KAAa;IAEhB,OAAOQ;AACT;AAEO,SAASZ,aACdY,WAA8C,EAC9CO,aAAqC;IAErC,MAAMC,iBAAiBf,IAAAA,aAAM,EAI1B;QACDgB,MAAM;QACN,0FAA0F;QAC1FC,OAAO,EAAE;QACTC,UAAUC;IACZ;IAEA,MAAMC,0BAA0BZ,IAAAA,kBAAW,EAAC,CAACa;QAC3C,MAAM,EAAEH,QAAQ,EAAED,KAAK,EAAE,GAAGF,eAAeb,OAAO;QAClD,IAAIgB,UAAU;YACZA,SAASG;QACX,OAAO;YACLJ,MAAOK,IAAI,CAACD;QACd;IACF,GAAG,EAAE;IAELpB,IAAAA,gBAAS,EAAC;QACR,MAAM,EAAEC,SAASqB,WAAW,EAAE,GAAGR;QACjC,2DAA2D;QAC3D,IAAIQ,YAAYP,IAAI,EAAE;YACpB;QACF;QACAO,YAAYP,IAAI,GAAG;QAEnB,MAAM,CACJ,gGAAgG;QAChG,iEACAQ,IAAI,CAAC;gBAAC,EAAEC,OAAO,EAAE;YACjB,MAAM,EAAEvB,OAAO,EAAE,GAAGa;YACpBU,QAAQ;gBACNC,oBAAmBC,EAAwC;oBACzDzB,QAAQgB,QAAQ,GAAGS;oBAEnB,iFAAiF;oBACjF,KAAK,MAAMN,OAAOnB,QAAQe,KAAK,CAAG;wBAChCU,GAAGN;oBACL;oBACAnB,QAAQe,KAAK,GAAGE;gBAClB;gBACAZ;gBACAO;YACF;QACF;IACF,GAAG;QAACP;QAAaO;KAAc;IAE/B,OAAOM;AACT;AAEO,SAASvB,iBACd+B,YAA6C;IAE7C,MAAMrB,cAAcb,eAAekC;IACnC,MAAM,EAAEC,IAAI,EAAE,GAAGC,IAAAA,iBAAU,EAACC,wDAAyB;IAErD9B,IAAAA,gBAAS,EAAC;QACR,yCAAyC;QACzC,MAAM+B,WAAWC,YAAY;YAC3B1B,YACE2B,KAAKC,SAAS,CAAC;gBACbC,OAAO;gBACPP;gBACAQ,aAAa;YACf;QAEJ,GAAG;QACH,OAAO,IAAMC,cAAcN;IAC7B,GAAG;QAACH;QAAMtB;KAAY;AACxB"}