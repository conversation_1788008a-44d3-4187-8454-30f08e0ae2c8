{"version": 3, "sources": ["../../../../src/build/webpack/plugins/nextjs-require-cache-hot-reloader.ts"], "names": ["NextJsRequireCacheHotReloader", "deleteAppClientCache", "deleteCache", "originModules", "require", "resolve", "RUNTIME_NAMES", "deleteFromRequireCache", "filePath", "realpathSync", "e", "isError", "code", "mod", "cache", "originModule", "parent", "idx", "children", "indexOf", "splice", "child", "clearManifestCache", "PLUGIN_NAME", "constructor", "opts", "prevAssets", "serverComponents", "apply", "compiler", "hooks", "assetEmitted", "tap", "_file", "targetPath", "clearModuleContext", "afterEmit", "tapPromise", "compilation", "name", "runtimeChunk<PERSON><PERSON>", "path", "join", "outputOptions", "hasAppEntry", "entries", "keys", "filter", "entry", "isAppPath", "toString", "startsWith", "page", "outputPath"], "mappings": ";;;;;;;;;;;;;;;;IAqEaA,6BAA6B;eAA7BA;;IArBGC,oBAAoB;eAApBA;;IAWAC,WAAW;eAAXA;;;yBA1DmB;0BACN;6DACZ;gEACG;8BACe;;;;;;AAKnC,MAAMC,gBAAgB;IACpBC,QAAQC,OAAO,CAAC;IAChBD,QAAQC,OAAO,CAAC;IAChBD,QAAQC,OAAO,CAAC;IAChBD,QAAQC,OAAO,CAAC;IAChBD,QAAQC,OAAO,CAAC;IAChBD,QAAQC,OAAO,CAAC;IAChBD,QAAQC,OAAO,CAAC;CACjB;AAED,MAAMC,gBAAgB;IAAC;IAAmB;CAAsB;AAEhE,SAASC,uBAAuBC,QAAgB;IAC9C,IAAI;QACFA,WAAWC,IAAAA,sBAAY,EAACD;IAC1B,EAAE,OAAOE,GAAG;QACV,IAAIC,IAAAA,gBAAO,EAACD,MAAMA,EAAEE,IAAI,KAAK,UAAU,MAAMF;IAC/C;IACA,MAAMG,MAAMT,QAAQU,KAAK,CAACN,SAAS;IACnC,IAAIK,KAAK;QACP,oDAAoD;QACpD,KAAK,MAAME,gBAAgBZ,cAAe;YACxC,MAAMa,SAASZ,QAAQU,KAAK,CAACC,aAAa;YAC1C,IAAIC,QAAQ;gBACV,MAAMC,MAAMD,OAAOE,QAAQ,CAACC,OAAO,CAACN;gBACpC,IAAII,OAAO,GAAGD,OAAOE,QAAQ,CAACE,MAAM,CAACH,KAAK;YAC5C;QACF;QACA,iDAAiD;QACjD,KAAK,MAAMI,SAASR,IAAIK,QAAQ,CAAE;YAChCG,MAAML,MAAM,GAAG;QACjB;QACA,OAAOZ,QAAQU,KAAK,CAACN,SAAS;QAC9B,OAAO;IACT;IACA,OAAO;AACT;AAEO,SAASP;IACdM,uBACEH,QAAQC,OAAO,CAAC;IAElBE,uBACEH,QAAQC,OAAO,CACb;AAGN;AAEO,SAASH,YAAYM,QAAgB;IAC1C,oCAAoC;IACpCc,IAAAA,gCAAkB,EAACd;IAEnBD,uBAAuBC;AACzB;AAEA,MAAMe,cAAc;AAGb,MAAMvB;IAIXwB,YAAYC,IAAmC,CAAE;aAHjDC,aAAkB;QAIhB,IAAI,CAACC,gBAAgB,GAAGF,KAAKE,gBAAgB;IAC/C;IAEAC,MAAMC,QAAkB,EAAE;QACxBA,SAASC,KAAK,CAACC,YAAY,CAACC,GAAG,CAACT,aAAa,CAACU,OAAO,EAAEC,UAAU,EAAE;YACjE,uCAAuC;YACvCC,IAAAA,2BAAkB,EAACD;YACnBhC,YAAYgC;QACd;QAEAL,SAASC,KAAK,CAACM,SAAS,CAACC,UAAU,CAACd,aAAa,OAAOe;YACtD,KAAK,MAAMC,QAAQjC,cAAe;gBAChC,MAAMkC,mBAAmBC,aAAI,CAACC,IAAI,CAChCJ,YAAYK,aAAa,CAACF,IAAI,EAC9B,CAAC,EAAEF,KAAK,GAAG,CAAC;gBAEdrC,YAAYsC;YACd;YAEA,8DAA8D;YAC9D,oDAAoD;YACpD,mCAAmC;YACnC,IAAII,cAAc;YAClB,MAAMC,UAAU;mBAAIP,YAAYO,OAAO,CAACC,IAAI;aAAG,CAACC,MAAM,CAAC,CAACC;gBACtD,MAAMC,YAAYD,MAAME,QAAQ,GAAGC,UAAU,CAAC;gBAC9C,IAAIF,WAAWL,cAAc;gBAC7B,OAAOI,MAAME,QAAQ,GAAGC,UAAU,CAAC,aAAaF;YAClD;YAEA,IAAIL,aAAa;gBACf3C;YACF;YAEA,KAAK,MAAMmD,QAAQP,QAAS;gBAC1B,MAAMQ,aAAaZ,aAAI,CAACC,IAAI,CAC1BJ,YAAYK,aAAa,CAACF,IAAI,EAC9BW,OAAO;gBAETlD,YAAYmD;YACd;QACF;IACF;AACF"}