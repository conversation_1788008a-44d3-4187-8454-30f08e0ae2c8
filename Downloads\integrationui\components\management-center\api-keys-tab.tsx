import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import ModelApiKeyInput from "@/components/management-center/model-api-key-input"
import { KeyRound, Palette, Share2 } from "lucide-react"

const socialModels = [
  { value: "gpt-4-social", label: "OpenAI GPT-4 Social" },
  { value: "claude-3-opus", label: "Anthropic Claude 3 Opus" },
  { value: "gemini-1.5-pro", label: "Google Gemini 1.5 Pro" },
]

export default function ApiKeysTab() {
  return (
    <div className="space-y-8">
      <div className="flex items-center">
        <KeyRound className="w-6 h-6 mr-3 text-accent-primary" />
        <h2 className="text-xl font-semibold text-foreground">API Key Management</h2>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg text-card-foreground">
            <Palette className="w-5 h-5 mr-2 text-muted-foreground" />
            Creative Studio
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 lg:grid-cols-2 gap-4"></CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg text-card-foreground">
            <Share2 className="w-5 h-5 mr-2 text-muted-foreground" />
            Social Station
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ModelApiKeyInput id="influencer-api-key" label="Influencer API Key" models={socialModels} />
          <ModelApiKeyInput id="social-agent-api-key" label="Social Agent API Key" models={socialModels} />
        </CardContent>
      </Card>
    </div>
  )
}
