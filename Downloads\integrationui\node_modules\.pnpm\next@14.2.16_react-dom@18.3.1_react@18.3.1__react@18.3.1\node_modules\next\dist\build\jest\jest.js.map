{"version": 3, "sources": ["../../../src/build/jest/jest.ts"], "names": ["nextJest", "getConfig", "dir", "conf", "loadConfig", "PHASE_TEST", "loadClosestPackageJson", "attempts", "Error", "mainP<PERSON>", "Array", "join", "require", "e", "setUpEnv", "dev", "loadEnvConfig", "Log", "options", "customJestConfig", "nextConfig", "jsConfig", "resolvedBaseUrl", "isEsmProject", "pagesDir", "serverComponents", "resolvedDir", "resolve", "packageConfig", "type", "findPagesDirResult", "findPagesDir", "appDir", "result", "loadJsConfig", "resolvedJestConfig", "loadBindings", "experimental", "useWasmBinary", "lockfilePatchPromise", "cur", "transpiled", "transpilePackages", "jestTransformerConfig", "modularizeImports", "swcPlugins", "compilerOptions", "compiler", "moduleNameMapper", "testPathIgnorePatterns", "transform", "transformIgnorePatterns", "replace", "watchPathIgnorePatterns"], "mappings": ";;;;+BAqCA;;;;;;;;;;;;;;;;;;;CAmBC,GACD;;;eAAwBA;;;qBAzDM;sBACA;+DACP;2BACI;qEACF;6DACJ;8BACQ;qBACsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAInD,eAAeC,UAAUC,GAAW;IAClC,MAAMC,OAAO,MAAMC,IAAAA,eAAU,EAACC,qBAAU,EAAEH;IAC1C,OAAOC;AACT;AAEA;;CAEC,GACD,SAASG,uBAAuBJ,GAAW,EAAEK,WAAW,CAAC;IACvD,IAAIA,WAAW,GAAG;QAChB,MAAM,IAAIC,MAAM;IAClB;IACA,IAAIC,WAAWF,aAAa,IAAI,OAAOG,MAAMH,UAAUI,IAAI,CAAC;IAC5D,IAAI;QACF,OAAOC,QAAQD,IAAAA,UAAI,EAACT,KAAKO,WAAW;IACtC,EAAE,OAAOI,GAAG;QACV,OAAOP,uBAAuBJ,KAAKK,WAAW;IAChD;AACF;AAEA,4EAA4E,GAC5E,SAASO,SAASZ,GAAW;IAC3B,MAAMa,MAAM;IACZC,IAAAA,kBAAa,EAACd,KAAKa,KAAKE;AAC1B;AAsBe,SAASjB,SAASkB,UAA4B,CAAC,CAAC;IAC7D,mBAAmB;IACnB,OAAO,CACLC;QAIA,oEAAoE;QACpE,qCAAqC;QACrC,OAAO;gBA8BcC,0BAULA;YAvCd,IAAIA;YACJ,IAAIC;YACJ,IAAIC;YACJ,IAAIC,eAAe;YACnB,IAAIC;YACJ,IAAIC;YAEJ,IAAIP,QAAQhB,GAAG,EAAE;gBACf,MAAMwB,cAAcC,IAAAA,aAAO,EAACT,QAAQhB,GAAG;gBACvC,MAAM0B,gBAAgBtB,uBAAuBoB;gBAC7CH,eAAeK,cAAcC,IAAI,KAAK;gBAEtCT,aAAa,MAAMnB,UAAUyB;gBAC7B,MAAMI,qBAAqBC,IAAAA,0BAAY,EAACL;gBACxCD,mBAAmB,CAAC,CAACK,mBAAmBE,MAAM;gBAC9CR,WAAWM,mBAAmBN,QAAQ;gBACtCV,SAASY;gBACT,4DAA4D;gBAC5D,MAAMO,SAAS,MAAMC,IAAAA,qBAAY,EAACR,aAAaN;gBAC/CC,WAAWY,OAAOZ,QAAQ;gBAC1BC,kBAAkBW,OAAOX,eAAe;YAC1C;YACA,4CAA4C;YAC5C,MAAMa,qBACJ,AAAC,CAAA,OAAOhB,qBAAqB,aACzB,MAAMA,qBACNA,gBAAe,KAAM,CAAC;YAE5B,mEAAmE;YACnE,MAAMiB,IAAAA,iBAAY,EAAChB,+BAAAA,2BAAAA,WAAYiB,YAAY,qBAAxBjB,yBAA0BkB,aAAa;YAE1D,IAAIC,yBAAoB,CAACC,GAAG,EAAE;gBAC5B,MAAMD,yBAAoB,CAACC,GAAG;YAChC;YAEA,MAAMC,aAAa,AAACrB,CAAAA,CAAAA,8BAAAA,WAAYsB,iBAAiB,KAAI,EAAE,AAAD,EAAG/B,IAAI,CAAC;YAE9D,MAAMgC,wBAA+C;gBACnDC,iBAAiB,EAAExB,8BAAAA,WAAYwB,iBAAiB;gBAChDC,UAAU,EAAEzB,+BAAAA,4BAAAA,WAAYiB,YAAY,qBAAxBjB,0BAA0ByB,UAAU;gBAChDC,eAAe,EAAE1B,8BAAAA,WAAY2B,QAAQ;gBACrC1B;gBACAC;gBACAG;gBACAF;gBACAC;YACF;YACA,OAAO;gBACL,GAAGW,kBAAkB;gBAErBa,kBAAkB;oBAChB,wCAAwC;oBACxC,qDAAqD;oBACrD,mCACEpC,QAAQe,OAAO,CAAC;oBAElB,2CAA2C;oBAC3C,0BAA0Bf,QAAQe,OAAO,CAAC;oBAE1C,uBAAuB;oBACvB,+CAA+Cf,QAAQe,OAAO,CAC5D,CAAC,uBAAuB,CAAC;oBAG3B,qDAAqD;oBACrD,gBAAgBf,QAAQe,OAAO,CAAC,CAAC,uBAAuB,CAAC;oBAEzD,oBAAoB;oBACpB,mBAAmBf,QAAQe,OAAO,CAAC;oBACnC,mBAAmB;oBACnB,kBAAkBf,QAAQe,OAAO,CAAC;oBAClC,sBAAsB;oBACtB,eAAef,QAAQe,OAAO,CAAC;oBAE/B,kEAAkE;oBAClE,iEAAiE;oBACjE,kBAAkB;oBAClB,GAAIQ,mBAAmBa,gBAAgB,IAAI,CAAC,CAAC;gBAC/C;gBACAC,wBAAwB;oBACtB,uCAAuC;oBACvC;oBACA,mDAAmD;oBACnD;oBACA,uEAAuE;oBACvE,mEAAmE;uBAC/Dd,mBAAmBc,sBAAsB,IAAI,EAAE;iBACpD;gBAEDC,WAAW;oBACT,2BAA2B;oBAC3B,8BAA8B;wBAC5BtC,QAAQe,OAAO,CAAC;wBAChBgB;qBACD;oBACD,wDAAwD;oBACxD,GAAIR,mBAAmBe,SAAS,IAAI,CAAC,CAAC;gBACxC;gBAEAC,yBAAyB;oBACvB,uFAAuF;uBACnFV,aACA;wBACE,CAAC,2BAA2B,EAAEA,WAAW,GAAG,CAAC;wBAC7C,CAAC,wBAAwB,EAAEA,WAAWW,OAAO,CAC3C,OACA,OACA,GAAG,CAAC;qBACP,GACD;wBAAC;qBAAiB;oBACtB,8DAA8D;oBAC9D;oBAEA,wEAAwE;oBACxE,iFAAiF;uBAC7EjB,mBAAmBgB,uBAAuB,IAAI,EAAE;iBACrD;gBACDE,yBAAyB;oBACvB,2DAA2D;oBAC3D;uBACIlB,mBAAmBkB,uBAAuB,IAAI,EAAE;iBACrD;YACH;QACF;IACF;AACF"}