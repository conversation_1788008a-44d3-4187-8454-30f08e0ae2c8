"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs"
import { PanelLeftClose, User } from "lucide-react"
import ProfileSettingsModal from "./profile-settings-modal"

export default function LeftPanel({ togglePanel }: { togglePanel: () => void }) {
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false)

  return (
    <>
      <aside className="w-72 flex flex-col bg-card border-r relative">
        <Button
          variant="ghost"
          size="icon"
          onClick={togglePanel}
          className="absolute top-2 right-2 z-10 text-muted-foreground hover:bg-secondary hover:text-foreground h-8 w-8"
          title="Collapse panel"
        >
          <PanelLeftClose className="w-5 h-5" />
        </Button>
        <div className="flex-1 flex flex-col">
          <Tabs defaultValue="workspaces" className="flex flex-col h-full">
            <TabsList className="grid w-full grid-cols-2 bg-card border-b rounded-none h-14">
              <TabsTrigger
                value="history"
                className="data-[state=active]:bg-accent-primary data-[state=active]:text-primary-foreground text-muted-foreground rounded-none"
              >
                History
              </TabsTrigger>
              <TabsTrigger
                value="workspaces"
                className="data-[state=active]:bg-accent-primary data-[state=active]:text-primary-foreground text-muted-foreground rounded-none"
              >
                Workspaces
              </TabsTrigger>
            </TabsList>
            <TabsContent value="workspaces" className="flex-1 overflow-y-auto p-4 mt-0">
              <div className="text-center text-muted-foreground p-8 text-sm">
                <p>Your active connections will be displayed here.</p>
              </div>
            </TabsContent>
            <TabsContent value="history" className="flex-1 overflow-y-auto p-4 mt-0">
              <div className="text-center text-muted-foreground p-8 text-sm">
                <p>Your chat history will appear here.</p>
              </div>
            </TabsContent>
          </Tabs>
        </div>
        <div className="mt-auto p-2 border-t">
          <Button
            variant="ghost"
            className="w-full justify-start text-left h-auto py-2 px-3"
            onClick={() => setIsProfileModalOpen(true)}
          >
            <div className="w-10 h-10 rounded-lg bg-secondary flex items-center justify-center mr-3">
              <User className="w-5 h-5 text-muted-foreground" />
            </div>
            <div>
              <p className="font-semibold text-sm text-foreground">John Doe</p>
              <p className="text-xs text-muted-foreground"><EMAIL></p>
            </div>
          </Button>
        </div>
      </aside>
      <ProfileSettingsModal isOpen={isProfileModalOpen} onClose={() => setIsProfileModalOpen(false)} />
    </>
  )
}
