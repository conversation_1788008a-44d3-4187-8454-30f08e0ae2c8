"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Input } from "@/components/ui/input"
import { Settings2 } from "lucide-react"

export default function OrchestratorConfig() {
  const [temperature, setTemperature] = useState(0.7)

  return (
    <Card className="bg-[#1C1C1C] border-gray-800">
      <CardHeader>
        <CardTitle className="flex items-center text-lg text-gray-200">
          <Settings2 className="w-5 h-5 mr-2 text-gray-400" />
          Orchestrator Configuration
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="primary-model" className="text-sm font-medium text-gray-400">
            Primary Model
          </Label>
          <Select defaultValue="gemini-pro">
            <SelectTrigger id="primary-model" className="w-full bg-[#252525] border-gray-700">
              <SelectValue placeholder="Select a model" />
            </SelectTrigger>
            <SelectContent className="bg-[#252525] border-gray-700 text-white">
              <SelectItem value="gemini-pro">Google Gemini Pro</SelectItem>
              <SelectItem value="gpt-4">OpenAI GPT-4</SelectItem>
              <SelectItem value="claude-3">Anthropic Claude 3</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label htmlFor="temperature" className="text-sm font-medium text-gray-400">
              Temperature
            </Label>
            <span className="text-sm text-[#069494]">{temperature.toFixed(1)}</span>
          </div>
          <Slider
            id="temperature"
            min={0}
            max={1}
            step={0.1}
            value={[temperature]}
            onValueChange={(value) => setTemperature(value[0])}
            className="[&>span:first-child]:h-1 [&>span:first-child>span]:bg-[#069494]"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="max-tokens" className="text-sm font-medium text-gray-400">
            Max Tokens
          </Label>
          <Input id="max-tokens" type="number" defaultValue={4096} className="bg-[#252525] border-gray-700" />
        </div>
      </CardContent>
    </Card>
  )
}
