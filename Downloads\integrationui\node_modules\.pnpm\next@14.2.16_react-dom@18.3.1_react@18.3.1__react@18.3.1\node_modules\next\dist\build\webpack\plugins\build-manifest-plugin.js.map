{"version": 3, "sources": ["../../../../src/build/webpack/plugins/build-manifest-plugin.ts"], "names": ["BuildManifestPlugin", "getEntrypointFiles", "normalizeRewritesForBuildManifest", "srcEmptySsgManifest", "buildNodejsLowPriorityPath", "filename", "buildId", "CLIENT_STATIC_FILES_PATH", "createEdgeRuntimeManifest", "originAssetMap", "manifestFilenames", "assetMap", "lowPriorityFiles", "manifestDefCode", "JSON", "stringify", "lowPriorityFilesCode", "map", "join", "normalizeRewrite", "item", "has", "source", "destination", "rewrites", "afterFiles", "beforeFiles", "fallback", "generateClientManifest", "compiler", "compilation", "compilationSpan", "spans", "get", "genClientManifestSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "clientManifest", "__rewrites", "appDependencies", "Set", "pages", "sortedPageKeys", "getSortedRoutes", "Object", "keys", "for<PERSON>ach", "page", "dependencies", "filteredDeps", "filter", "dep", "length", "sortedPages", "devalue", "entrypoint", "getFiles", "file", "test", "replace", "processRoute", "r", "rewrite", "startsWith", "constructor", "options", "isDev<PERSON><PERSON><PERSON>", "appDirEnabled", "exportRuntime", "createAssets", "assets", "createAssetsSpan", "entrypoints", "polyfillFiles", "devFiles", "ampDevFiles", "rootMainFiles", "ampFirstPages", "ampFirstEntryNames", "ampFirstEntryNamesMap", "entryName", "pagePath", "getRouteFromEntrypoint", "push", "mainFiles", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "compilationAssets", "getAssets", "p", "name", "endsWith", "info", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "v", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "values", "SYSTEM_ENTRYPOINTS", "filesForPage", "buildManifestPath", "ssgManifestPath", "sources", "RawSource", "sort", "reduce", "a", "c", "buildManifestName", "BUILD_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "clientManifestPath", "apply", "hooks", "make", "tap", "processAssets", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": ";;;;;;;;;;;;;;;;;IAuJA,iFAAiF;IACjF,+GAA+G;IAC/G,OAoLC;eApLoBA;;IAzBLC,kBAAkB;eAAlBA;;IArDAC,iCAAiC;eAAjCA;;IA9CHC,mBAAmB;eAAnBA;;;gEA5BO;yBACa;2BAW1B;+EAE4B;0CACG;uBACN;iCACV;;;;;;AAWf,MAAMA,sBAAsB,CAAC,4EAA4E,CAAC;AAEjH,+CAA+C;AAC/C,SAASC,2BAA2BC,QAAgB,EAAEC,OAAe;IACnE,OAAO,CAAC,EAAEC,mCAAwB,CAAC,CAAC,EAAED,QAAQ,CAAC,EAAED,SAAS,CAAC;AAC7D;AAEA,SAASG,0BAA0BC,cAA6B;IAC9D,MAAMC,oBAAoB;QAAC;QAAqB;KAAkB;IAElE,MAAMC,WAA0B;QAC9B,GAAGF,cAAc;QACjBG,kBAAkB,EAAE;IACtB;IAEA,MAAMC,kBAAkB,CAAC,wBAAwB,EAAEC,KAAKC,SAAS,CAC/DJ,UACA,MACA,GACA,GAAG,CAAC;IACN,+FAA+F;IAC/F,gIAAgI;IAChI,MAAMK,uBACJ,CAAC,4CAA4C,CAAC,GAC9CN,kBACGO,GAAG,CAAC,CAACZ;QACJ,OAAO,CAAC,6CAA6C,EAAEA,SAAS,IAAI,CAAC;IACvE,GACCa,IAAI,CAAC,OACR,CAAC,IAAI,CAAC;IAER,OAAOL,kBAAkBG;AAC3B;AAEA,SAASG,iBAAiBC,IAIzB;IACC,OAAO;QACLC,KAAKD,KAAKC,GAAG;QACbC,QAAQF,KAAKE,MAAM;QACnBC,aAAaH,KAAKG,WAAW;IAC/B;AACF;AAEO,SAASrB,kCACdsB,QAAkC;QAGpBA,sBACCA,uBACHA;IAHZ,OAAO;QACLC,UAAU,GAAED,uBAAAA,SAASC,UAAU,qBAAnBD,qBAAqBP,GAAG,CAAC,CAACG,OAASD,iBAAiBC;QAChEM,WAAW,GAAEF,wBAAAA,SAASE,WAAW,qBAApBF,sBAAsBP,GAAG,CAAC,CAACG,OAASD,iBAAiBC;QAClEO,QAAQ,GAAEH,qBAAAA,SAASG,QAAQ,qBAAjBH,mBAAmBP,GAAG,CAAC,CAACG,OAASD,iBAAiBC;IAC9D;AACF;AAEA,mFAAmF;AACnF,yCAAyC;AACzC,SAASQ,uBACPC,QAAa,EACbC,WAAgB,EAChBnB,QAAuB,EACvBa,QAAkC;IAElC,MAAMO,kBAAkBC,sBAAK,CAACC,GAAG,CAACH,gBAAgBE,sBAAK,CAACC,GAAG,CAACJ;IAC5D,MAAMK,wBAAwBH,mCAAAA,gBAAiBI,UAAU,CACvD;IAGF,OAAOD,yCAAAA,sBAAuBE,OAAO,CAAC;QACpC,MAAMC,iBAAsC;YAC1CC,YAAYpC,kCAAkCsB;QAChD;QACA,MAAMe,kBAAkB,IAAIC,IAAI7B,SAAS8B,KAAK,CAAC,QAAQ;QACvD,MAAMC,iBAAiBC,IAAAA,sBAAe,EAACC,OAAOC,IAAI,CAAClC,SAAS8B,KAAK;QAEjEC,eAAeI,OAAO,CAAC,CAACC;YACtB,MAAMC,eAAerC,SAAS8B,KAAK,CAACM,KAAK;YAEzC,IAAIA,SAAS,SAAS;YACtB,6EAA6E;YAC7E,wDAAwD;YACxD,MAAME,eAAeD,aAAaE,MAAM,CACtC,CAACC,MAAQ,CAACZ,gBAAgBlB,GAAG,CAAC8B;YAGhC,2DAA2D;YAC3D,IAAIF,aAAaG,MAAM,EAAE;gBACvBf,cAAc,CAACU,KAAK,GAAGE;YACzB;QACF;QACA,6EAA6E;QAC7E,qEAAqE;QACrEZ,eAAegB,WAAW,GAAGX;QAE7B,OAAOY,IAAAA,gBAAO,EAACjB;IACjB;AACF;AAEO,SAASpC,mBAAmBsD,UAAe;IAChD,OACEA,CAAAA,8BAAAA,WACIC,QAAQ,GACTN,MAAM,CAAC,CAACO;QACP,wEAAwE;QACxE,OAAO,oCAAoCC,IAAI,CAACD;IAClD,GACCxC,GAAG,CAAC,CAACwC,OAAiBA,KAAKE,OAAO,CAAC,OAAO,UAAS,EAAE;AAE5D;AAEA,MAAMC,eAAe,CAACC;IACpB,MAAMC,UAAU;QAAE,GAAGD,CAAC;IAAC;IAEvB,wDAAwD;IACxD,sBAAsB;IACtB,IAAI,CAACC,QAAQvC,WAAW,CAACwC,UAAU,CAAC,MAAM;QACxC,OAAO,AAACD,QAAgBvC,WAAW;IACrC;IACA,OAAOuC;AACT;AAIe,MAAM9D;IAOnBgE,YAAYC,OAMX,CAAE;QACD,IAAI,CAAC3D,OAAO,GAAG2D,QAAQ3D,OAAO;QAC9B,IAAI,CAAC4D,aAAa,GAAG,CAAC,CAACD,QAAQC,aAAa;QAC5C,IAAI,CAAC1C,QAAQ,GAAG;YACdE,aAAa,EAAE;YACfD,YAAY,EAAE;YACdE,UAAU,EAAE;QACd;QACA,IAAI,CAACwC,aAAa,GAAGF,QAAQE,aAAa;QAC1C,IAAI,CAAC3C,QAAQ,CAACE,WAAW,GAAGuC,QAAQzC,QAAQ,CAACE,WAAW,CAACT,GAAG,CAAC2C;QAC7D,IAAI,CAACpC,QAAQ,CAACC,UAAU,GAAGwC,QAAQzC,QAAQ,CAACC,UAAU,CAACR,GAAG,CAAC2C;QAC3D,IAAI,CAACpC,QAAQ,CAACG,QAAQ,GAAGsC,QAAQzC,QAAQ,CAACG,QAAQ,CAACV,GAAG,CAAC2C;QACvD,IAAI,CAACQ,aAAa,GAAG,CAAC,CAACH,QAAQG,aAAa;IAC9C;IAEAC,aAAaxC,QAAa,EAAEC,WAAgB,EAAEwC,MAAW,EAAE;QACzD,MAAMvC,kBAAkBC,sBAAK,CAACC,GAAG,CAACH,gBAAgBE,sBAAK,CAACC,GAAG,CAACJ;QAC5D,MAAM0C,mBAAmBxC,mCAAAA,gBAAiBI,UAAU,CAClD;QAEF,OAAOoC,oCAAAA,iBAAkBnC,OAAO,CAAC;YAC/B,MAAMoC,cAAgC1C,YAAY0C,WAAW;YAC7D,MAAM7D,WAAuC;gBAC3C8D,eAAe,EAAE;gBACjBC,UAAU,EAAE;gBACZC,aAAa,EAAE;gBACf/D,kBAAkB,EAAE;gBACpBgE,eAAe,EAAE;gBACjBnC,OAAO;oBAAE,SAAS,EAAE;gBAAC;gBACrBoC,eAAe,EAAE;YACnB;YAEA,MAAMC,qBAAqBC,+CAAqB,CAAC9C,GAAG,CAACH;YACrD,IAAIgD,oBAAoB;gBACtB,KAAK,MAAME,aAAaF,mBAAoB;oBAC1C,MAAMG,WAAWC,IAAAA,+BAAsB,EAACF;oBACxC,IAAI,CAACC,UAAU;wBACb;oBACF;oBAEAtE,SAASkE,aAAa,CAACM,IAAI,CAACF;gBAC9B;YACF;YAEA,MAAMG,YAAY,IAAI5C,IACpBvC,mBAAmBuE,YAAYvC,GAAG,CAACoD,2CAAgC;YAGrE,IAAI,IAAI,CAAClB,aAAa,EAAE;gBACtBxD,SAASiE,aAAa,GAAG;uBACpB,IAAIpC,IACLvC,mBACEuE,YAAYvC,GAAG,CAACqD,+CAAoC;iBAGzD;YACH;YAEA,MAAMC,oBAIAzD,YAAY0D,SAAS;YAE3B7E,SAAS8D,aAAa,GAAGc,kBACtBrC,MAAM,CAAC,CAACuC;gBACP,2CAA2C;gBAC3C,IAAI,CAACA,EAAEC,IAAI,CAACC,QAAQ,CAAC,QAAQ;oBAC3B,OAAO;gBACT;gBAEA,OACEF,EAAEG,IAAI,IAAIC,uDAA4C,IAAIJ,EAAEG,IAAI;YAEpE,GACC3E,GAAG,CAAC,CAAC6E,IAAMA,EAAEJ,IAAI;YAEpB/E,SAAS+D,QAAQ,GAAGzE,mBAClBuE,YAAYvC,GAAG,CAAC8D,oDAAyC,GACzD7C,MAAM,CAAC,CAACO,OAAS,CAAC2B,UAAU/D,GAAG,CAACoC;YAElC9C,SAASgE,WAAW,GAAG1E,mBACrBuE,YAAYvC,GAAG,CAAC+D,0CAA+B;YAGjD,KAAK,MAAMzC,cAAczB,YAAY0C,WAAW,CAACyB,MAAM,GAAI;gBACzD,IAAIC,6BAAkB,CAAC7E,GAAG,CAACkC,WAAWmC,IAAI,GAAG;gBAC7C,MAAMT,WAAWC,IAAAA,+BAAsB,EAAC3B,WAAWmC,IAAI;gBAEvD,IAAI,CAACT,UAAU;oBACb;gBACF;gBAEA,MAAMkB,eAAelG,mBAAmBsD;gBAExC5C,SAAS8B,KAAK,CAACwC,SAAS,GAAG;uBAAI,IAAIzC,IAAI;2BAAI4C;2BAAce;qBAAa;iBAAE;YAC1E;YAEA,IAAI,CAAC,IAAI,CAACjC,aAAa,EAAE;gBACvB,qEAAqE;gBACrE,uEAAuE;gBACvE,4BAA4B;gBAC5B,MAAMkC,oBAAoBhG,2BACxB,qBACA,IAAI,CAACE,OAAO;gBAEd,MAAM+F,kBAAkBjG,2BACtB,mBACA,IAAI,CAACE,OAAO;gBAEdK,SAASC,gBAAgB,CAACuE,IAAI,CAACiB,mBAAmBC;gBAClD/B,MAAM,CAAC+B,gBAAgB,GAAG,IAAIC,gBAAO,CAACC,SAAS,CAACpG;YAClD;YAEAQ,SAAS8B,KAAK,GAAGG,OAAOC,IAAI,CAAClC,SAAS8B,KAAK,EACxC+D,IAAI,GACJC,MAAM,CACL,2BAA2B;YAC3B,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGhG,SAAS8B,KAAK,CAACkE,EAAE,EAAGD,CAAAA,GACvC,CAAC;YAGL,IAAIE,oBAAoBC,yBAAc;YAEtC,IAAI,IAAI,CAAC3C,aAAa,EAAE;gBACtB0C,oBAAoB,CAAC,SAAS,EAAEC,yBAAc,CAAC,CAAC;YAClD;YAEAvC,MAAM,CAACsC,kBAAkB,GAAG,IAAIN,gBAAO,CAACC,SAAS,CAC/CzF,KAAKC,SAAS,CAACJ,UAAU,MAAM;YAGjC2D,MAAM,CAAC,CAAC,OAAO,EAAEwC,oCAAyB,CAAC,GAAG,CAAC,CAAC,GAAG,IAAIR,gBAAO,CAACC,SAAS,CACtE,CAAC,EAAE/F,0BAA0BG,UAAU,CAAC;YAG1C,IAAI,CAAC,IAAI,CAACuD,aAAa,EAAE;gBACvB,MAAM6C,qBAAqB,CAAC,EAAExG,mCAAwB,CAAC,CAAC,EAAE,IAAI,CAACD,OAAO,CAAC,kBAAkB,CAAC;gBAE1FgE,MAAM,CAACyC,mBAAmB,GAAG,IAAIT,gBAAO,CAACC,SAAS,CAChD,CAAC,wBAAwB,EAAE3E,uBACzBC,UACAC,aACAnB,UACA,IAAI,CAACa,QAAQ,EACb,uDAAuD,CAAC;YAE9D;YAEA,OAAO8C;QACT;IACF;IAEA0C,MAAMnF,QAA0B,EAAE;QAChCA,SAASoF,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,uBAAuB,CAACrF;YAC9CA,YAAYmF,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACEzB,MAAM;gBACN2B,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAAClD;gBACC,IAAI,CAACD,YAAY,CAACxC,UAAUC,aAAawC;YAC3C;QAEJ;QACA;IACF;AACF"}